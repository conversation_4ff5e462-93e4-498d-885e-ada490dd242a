package ru.oskelly.concierge.config.statemachine;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.StateMachineFactory;
import org.springframework.statemachine.persist.StateMachineRuntimePersister;
import org.springframework.statemachine.support.DefaultStateMachineContext;
import org.springframework.statemachine.test.StateMachineTestPlan;
import org.springframework.statemachine.test.StateMachineTestPlanBuilder;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.statemachine.action.AssignSalesToBitrixDealAction;
import ru.oskelly.concierge.statemachine.action.ErrorHandleAction;
import ru.oskelly.concierge.statemachine.action.OrderAssignmentSalesAction;
import ru.oskelly.concierge.statemachine.action.OrderAssignmentSourcerAction;
import ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent;
import ru.oskelly.concierge.statemachine.listener.CustomStateMachineListener;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.AWAITING_CLIENT_ANSWER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.AWAITING_SEND_TO_CLIENT;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.AWAITING_SOURCER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.CANCELLED;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.DONE;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.DRAFT;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.IN_PROGRESS_SALES;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.IN_PROGRESS_SOURCER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.NEW;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.PAYED_ORDER_IN_PROGRESS;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.PAYED_REPEAT_REQUEST;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.PAYED_RR_SOURCER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.REJECTED;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.REPEAT_AWAITING_SOURCER;
import static ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum.REPEAT_REQUEST_TO_SALES;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.BUYER_FAILED;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.CLIENT_REPEAT_REQUEST;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.ORDER_DONE;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.PAYED_BY_CUSTOMER;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.PROCEED_ORDER;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.RESTORE_BY_SALES;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SALES_PURCHASE_CANCELED;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SEND_PROPOSAL_TO_SALES;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SEND_PURPOSAL_TO_CLIENT;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SEND_TO_REJECTION;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SEND_TO_SALES;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SEND_TO_SOURCER;
import static ru.oskelly.concierge.statemachine.events.PurchaseOrderTransitionEvent.SOURCER_PURCHASE_CANCELED;

@SpringBootTest(classes = {StateMachineConfig.class}) // Указываем только StateMachineConfig для фокуса
@DisplayName("StateMachineConfig Transitions Tests")
class StateMachineConfigTest {

    @Autowired
    private StateMachineFactory<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachineFactory;

    @MockitoBean
    private StateMachineRuntimePersister<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent, String> stateMachineRuntimePersister;

    @MockitoBean
    private CustomStateMachineListener customStateMachineListener;

    @MockitoBean
    private OrderAssignmentSalesAction orderAssignmentSalesAction;

    @MockitoBean
    private OrderAssignmentSourcerAction orderAssignmentSourcerAction;

    @MockitoBean
    private AssignSalesToBitrixDealAction assignSalesToBitrixDealAction;

    @MockitoBean
    private ErrorHandleAction errorHandleAction;

    private StateMachine<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> stateMachine;

    private final Long DEFAULT_USER_ID = 1L;
    private final Long OTHER_USER_ID = 2L; // Для проверки несовпадения в guard

    @BeforeEach
    void setUp() {
        stateMachine = stateMachineFactory.getStateMachine(UUID.randomUUID().toString());
        // StateMachineTestPlan позаботится о запуске машины.
        // Очищаем ThreadLocalContext перед каждым тестом, если он используется guard'ами
        ThreadLocalContext.clean();
    }

    @AfterEach
    void tearDown() {
        if (stateMachine != null && stateMachine.isComplete()) {
            // stateMachine.stop(); // StateMachineTestPlan также должен обрабатывать остановку
        }
        // Очищаем ThreadLocalContext после каждого теста
        ThreadLocalContext.clean();
    }

    private StateMachineTestPlanBuilder<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> planBuilder() {
        // Этот builder использует stateMachine, инициализированный в setUp
        // или пере-инициализированный/сброшенный в самом тесте
        return StateMachineTestPlanBuilder.<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent>builder()
                .defaultAwaitTime(2) // seconds
                .stateMachine(stateMachine);
    }

    private void resetStateMachineToState(PurchaseOrderStatusEnum targetState) {
        // Убедимся, что машина остановлена перед сбросом состояния, если она была запущена
        if (stateMachine.isComplete() || stateMachine.getState() != null) { // state.getState() != null может быть признаком активности
            // stateMachine.stopReactively().block(); // Остановка может быть не нужна, если reset это делает
        }
        stateMachine.getStateMachineAccessor().doWithAllRegions(access ->
                access.resetStateMachineReactively(new DefaultStateMachineContext<>(
                        targetState, null, null, null, null, stateMachine.getId()
                )).block()
        );
        assertEquals(targetState, stateMachine.getState().getId(), "State machine should be reset to " + targetState);
    }


    @Test
    @DisplayName("DRAFT: Manual to NEW via SEND_TO_SALES event")
    void testDraft_ToNew_WithSendToSalesEvent() throws Exception {
        // Для ручного перехода guard'ы isNew и isAwaitingSourcer должны быть false
        stateMachine.getExtendedState().getVariables().put("isNew", false);
        stateMachine.getExtendedState().getVariables().put("isAwaitingSourcer", false);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step() // Убедимся, что мы все еще в DRAFT
                        .expectStates(DRAFT)
                        .and()
                        .step()
                        .sendEvent(SEND_TO_SALES)
                        .expectStateChanged(1)
                        .expectStates(NEW)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("NEW: To CANCELLED via SALES_PURCHASE_CANCELED event")
    void testNew_ToCancelled_WithSalesPurchaseCanceledEvent() throws Exception {
        resetStateMachineToState(NEW);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(NEW).and()
                        .step()
                        .sendEvent(SALES_PURCHASE_CANCELED)
                        .expectStateChanged(1)
                        .expectStates(CANCELLED)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("NEW: To REJECTED via SEND_TO_REJECTION event")
    void testNew_ToRejected_WithSendToRejectionEvent() throws Exception {
        resetStateMachineToState(NEW);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(NEW).and()
                        .step()
                        .sendEvent(SEND_TO_REJECTION)
                        .expectStateChanged(1)
                        .expectStates(REJECTED)
                        .and()
                        .build();
        plan.test();
    }

    // --- 3. Переходы из IN_PROGRESS_SALES ---

    @Test
    @DisplayName("IN_PROGRESS_SALES: To AWAITING_SOURCER via SEND_TO_SOURCER event")
    void testInProgressSales_ToAwaitingSourcer_WithSendToSourcerEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SALES);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SALES).and()
                        .step()
                        .sendEvent(SEND_TO_SOURCER)
                        .expectStateChanged(1)
                        .expectStates(AWAITING_SOURCER)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("IN_PROGRESS_SALES: To CANCELLED via SALES_PURCHASE_CANCELED event")
    void testInProgressSales_ToCancelled_WithSalesPurchaseCanceledEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SALES);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SALES).and()
                        .step()
                        .sendEvent(SALES_PURCHASE_CANCELED)
                        .expectStateChanged(1)
                        .expectStates(CANCELLED)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("IN_PROGRESS_SALES: To REJECTED via SEND_TO_REJECTION event")
    void testInProgressSales_ToRejected_WithSendToRejectionEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SALES);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SALES).and()
                        .step()
                        .sendEvent(SEND_TO_REJECTION)
                        .expectStateChanged(1)
                        .expectStates(REJECTED)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("AWAITING_SOURCER: To REJECTED via SEND_TO_REJECTION event")
    void testAwaitingSourcer_ToRejected_WithSendToRejectionEvent() throws Exception {
        resetStateMachineToState(AWAITING_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(AWAITING_SOURCER).and()
                        .step()
                        .sendEvent(SEND_TO_REJECTION)
                        .expectStateChanged(1)
                        .expectStates(REJECTED)
                        .and()
                        .build();
        plan.test();
    }

    // --- 5. Переходы из IN_PROGRESS_SOURCER ---

    @Test
    @DisplayName("IN_PROGRESS_SOURCER: To AWAITING_SEND_TO_CLIENT via SEND_PROPOSAL_TO_SALES event")
    void testInProgressSourcer_ToAwaitingSendToClient_WithSendProposalToSalesEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SOURCER).and()
                        .step()
                        .sendEvent(SEND_PROPOSAL_TO_SALES)
                        .expectStateChanged(1)
                        .expectStates(AWAITING_SEND_TO_CLIENT)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("IN_PROGRESS_SOURCER: To CANCELLED via SOURCER_PURCHASE_CANCELED event")
    void testInProgressSourcer_ToCancelled_WithSourcerPurchaseCanceledEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SOURCER).and()
                        .step()
                        .sendEvent(SOURCER_PURCHASE_CANCELED)
                        .expectStateChanged(1)
                        .expectStates(CANCELLED)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("IN_PROGRESS_SOURCER: To REJECTED via SEND_TO_REJECTION event")
    void testInProgressSourcer_ToRejected_WithSendToRejectionEvent() throws Exception {
        resetStateMachineToState(IN_PROGRESS_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(IN_PROGRESS_SOURCER).and()
                        .step()
                        .sendEvent(SEND_TO_REJECTION)
                        .expectStateChanged(1)
                        .expectStates(REJECTED)
                        .and()
                        .build();
        plan.test();
    }

    // --- 6. Переходы из AWAITING_SEND_TO_CLIENT ---

    @Test
    @DisplayName("AWAITING_SEND_TO_CLIENT: To AWAITING_CLIENT_ANSWER via SEND_PURPOSAL_TO_CLIENT event")
    void testAwaitingSendToClient_ToAwaitingClientAnswer_WithSendPurposalToClientEvent() throws Exception {
        resetStateMachineToState(AWAITING_SEND_TO_CLIENT);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(AWAITING_SEND_TO_CLIENT).and()
                        .step()
                        .sendEvent(SEND_PURPOSAL_TO_CLIENT)
                        .expectStateChanged(1)
                        .expectStates(AWAITING_CLIENT_ANSWER)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("AWAITING_SEND_TO_CLIENT: To REJECTED via SEND_TO_REJECTION event")
    void testAwaitingSendToClient_ToRejected_WithSendToRejectionEvent() throws Exception {
        resetStateMachineToState(AWAITING_SEND_TO_CLIENT);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(AWAITING_SEND_TO_CLIENT).and()
                        .step()
                        .sendEvent(SEND_TO_REJECTION)
                        .expectStateChanged(1)
                        .expectStates(REJECTED)
                        .and()
                        .build();
        plan.test();
    }

    // --- 7. Переходы из AWAITING_CLIENT_ANSWER ---

    @Test
    @DisplayName("AWAITING_CLIENT_ANSWER: To REPEAT_REQUEST_TO_SALES via CLIENT_REPEAT_REQUEST event")
    void testAwaitingClientAnswer_ToRepeatRequestToSales_WithClientRepeatRequestEvent() throws Exception {
        resetStateMachineToState(AWAITING_CLIENT_ANSWER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(AWAITING_CLIENT_ANSWER).and()
                        .step()
                        .sendEvent(CLIENT_REPEAT_REQUEST)
                        .expectStateChanged(1)
                        .expectStates(REPEAT_REQUEST_TO_SALES)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("AWAITING_CLIENT_ANSWER: To PAYED_ORDER_IN_PROGRESS via PAYED_BY_CUSTOMER event")
    void testAwaitingClientAnswer_ToPayedOrderInProgress_WithPayedByCustomerEvent() throws Exception {
        resetStateMachineToState(AWAITING_CLIENT_ANSWER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(AWAITING_CLIENT_ANSWER).and()
                        .step()
                        .sendEvent(PAYED_BY_CUSTOMER)
                        .expectStateChanged(1)
                        .expectStates(DONE)
                        .and()
                        .build();
        plan.test();
    }

    // --- 8. Переходы из REPEAT_REQUEST_TO_SALES ---

    @Test
    @DisplayName("REPEAT_REQUEST_TO_SALES: To REPEAT_AWAITING_SOURCER via SEND_TO_SOURCER event")
    void testRepeatRequestToSales_ToRepeatAwaitingSourcer_WithSendToSourcerEvent() throws Exception {
        resetStateMachineToState(REPEAT_REQUEST_TO_SALES);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(REPEAT_REQUEST_TO_SALES).and()
                        .step()
                        .sendEvent(SEND_TO_SOURCER)
                        .expectStateChanged(1)
                        .expectStates(REPEAT_AWAITING_SOURCER)
                        .and()
                        .build();
        plan.test();
    }

    // --- 9. Переходы из REPEAT_AWAITING_SOURCER ---

    @Test
    @DisplayName("REPEAT_AWAITING_SOURCER: To AWAITING_SEND_TO_CLIENT via SEND_PROPOSAL_TO_SALES event")
    void testRepeatAwaitingSourcer_ToAwaitingSendToClient_WithSendProposalToSalesEvent() throws Exception {
        resetStateMachineToState(REPEAT_AWAITING_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(REPEAT_AWAITING_SOURCER).and()
                        .step()
                        .sendEvent(SEND_PROPOSAL_TO_SALES)
                        .expectStateChanged(1)
                        .expectStates(AWAITING_SEND_TO_CLIENT)
                        .and()
                        .build();
        plan.test();
    }

    // --- 10. Переходы из PAYED_ORDER_IN_PROGRESS ---

    @Test
    @DisplayName("PAYED_ORDER_IN_PROGRESS: To PAYED_REPEAT_REQUEST via BUYER_FAILED event")
    void testPayedOrderInProgress_ToPayedRepeatRequest_WithBuyerFailedEvent() throws Exception {
        resetStateMachineToState(PAYED_ORDER_IN_PROGRESS);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(PAYED_ORDER_IN_PROGRESS).and()
                        .step()
                        .sendEvent(BUYER_FAILED)
                        .expectStateChanged(1)
                        .expectStates(PAYED_REPEAT_REQUEST)
                        .and()
                        .build();
        plan.test();
    }

    @Test
    @DisplayName("PAYED_ORDER_IN_PROGRESS: To DONE via ORDER_DONE event")
    void testPayedOrderInProgress_ToDone_WithOrderDoneEvent() throws Exception {
        resetStateMachineToState(PAYED_ORDER_IN_PROGRESS);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(PAYED_ORDER_IN_PROGRESS).and()
                        .step()
                        .sendEvent(ORDER_DONE)
                        .expectStateChanged(1)
                        .expectStates(DONE)
                        .and()
                        .build();
        plan.test();
    }

    // --- 11. Переходы из PAYED_REPEAT_REQUEST ---

    @Test
    @DisplayName("PAYED_REPEAT_REQUEST: To PAYED_RR_SOURCER via PROCEED_ORDER event")
    void testPayedRepeatRequest_ToPayedRrSourcer_WithProceedOrderEvent() throws Exception {
        resetStateMachineToState(PAYED_REPEAT_REQUEST);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(PAYED_REPEAT_REQUEST).and()
                        .step()
                        .sendEvent(PROCEED_ORDER)
                        .expectStateChanged(1)
                        .expectStates(PAYED_RR_SOURCER)
                        .and()
                        .build();
        plan.test();
    }

    // --- 12. Переходы из PAYED_RR_SOURCER ---

    @Test
    @DisplayName("PAYED_RR_SOURCER: To PAYED_ORDER_IN_PROGRESS via PROCEED_ORDER event")
    void testPayedRrSourcer_ToPayedOrderInProgress_WithProceedOrderEvent() throws Exception {
        resetStateMachineToState(PAYED_RR_SOURCER);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(PAYED_RR_SOURCER).and()
                        .step()
                        .sendEvent(PROCEED_ORDER)
                        .expectStateChanged(1)
                        .expectStates(PAYED_ORDER_IN_PROGRESS)
                        .and()
                        .build();
        plan.test();
    }

    // --- 13. Переходы из REJECTED ---

    @Test
    @DisplayName("REJECTED: To IN_PROGRESS_SALES via RESTORE_BY_SALES event")
    void testRejected_ToInProgressSales_WithRestoreBySalesEvent() throws Exception {
        resetStateMachineToState(REJECTED);

        StateMachineTestPlan<PurchaseOrderStatusEnum, PurchaseOrderTransitionEvent> plan =
                planBuilder()
                        .step().expectStates(REJECTED).and()
                        .step()
                        .sendEvent(RESTORE_BY_SALES)
                        .expectStateChanged(1)
                        .expectStates(IN_PROGRESS_SALES)
                        .and()
                        .build();
        plan.test();
    }
}