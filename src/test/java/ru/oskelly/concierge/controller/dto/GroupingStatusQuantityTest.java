package ru.oskelly.concierge.controller.dto;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;

import static org.junit.jupiter.api.Assertions.*;

class GroupingStatusQuantityTest {

    @BeforeEach
    void setUp() {
        ThreadLocalContext.clean();
    }

    @AfterEach
    void tearDown() {
        ThreadLocalContext.clean();
    }

    @Test
    void testConstructorWithValidRole() {
        // Given
        ThreadLocalContext.put(ContextConstants.ROLE, Roles.SALES);
        PurchaseOrderStatusEnum status = PurchaseOrderStatusEnum.NEW;
        Long quantity = 5L;

        // When
        GroupingStatusQuantity result = new GroupingStatusQuantity(status, quantity);

        // Then
        assertEquals(status.name(), result.statusId().code());
        assertEquals(status.getDescription(Roles.SALES), result.statusId().localizedDescription());
        assertEquals(quantity, result.quantity());
        assertNotNull(result.description());
    }

    @Test
    void testConstructorWithNullRole() {
        // Given - no role in context (will return null)
        PurchaseOrderStatusEnum status = PurchaseOrderStatusEnum.NEW;
        Long quantity = 5L;

        // When - should handle gracefully now
        GroupingStatusQuantity result = new GroupingStatusQuantity(status, quantity);

        // Then - should use default description
        assertEquals(status.name(), result.statusId().code());
        assertEquals(status.getDescription(null), result.statusId().localizedDescription());
        assertEquals(quantity, result.quantity());
        assertEquals(status.getDefaultDescription(), result.description());
    }

    @Test
    void testConstructorWithNullStatusId() {
        // Given
        ThreadLocalContext.put(ContextConstants.ROLE, Roles.SALES);
        Long quantity = 5L;

        // When - should handle gracefully now
        GroupingStatusQuantity result = new GroupingStatusQuantity(null, quantity);

        // Then - should use "Unknown status" description
        assertEquals("UNKNOWN", result.statusId().code());
        assertEquals("Unknown status", result.statusId().localizedDescription());
        assertEquals(quantity, result.quantity());
        assertEquals("Unknown status", result.description());
    }

    @Test
    void testConstructorWithWrongTypeInContext() {
        // Given - put wrong type in context
        ThreadLocalContext.put(ContextConstants.ROLE, "INVALID_ROLE");
        PurchaseOrderStatusEnum status = PurchaseOrderStatusEnum.NEW;
        Long quantity = 5L;

        // When - should handle gracefully now
        GroupingStatusQuantity result = new GroupingStatusQuantity(status, quantity);

        // Then - should use default description (fallback to null role)
        assertEquals(status.name(), result.statusId().code());
        assertEquals(status.getDescription(null), result.statusId().localizedDescription());
        assertEquals(quantity, result.quantity());
        assertEquals(status.getDefaultDescription(), result.description());
    }

    @Test
    void testBuilderConstructor() {
        // Given
        DescriptionStructureEnum statusDescription = new DescriptionStructureEnum("NEW", "New Status", "new status info");
        Long quantity = 5L;
        String description = "Test description";

        // When
        GroupingStatusQuantity result = GroupingStatusQuantity.builder()
                .statusId(statusDescription)
                .quantity(quantity)
                .description(description)
                .build();

        // Then
        assertEquals(statusDescription, result.statusId());
        assertEquals(quantity, result.quantity());
        assertEquals(description, result.description());
    }
}