package ru.oskelly.concierge.data.mapper;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mapstruct.factory.Mappers;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShortOfferDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferStatus;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Тесты маппера ShortOffer")
class ShortOfferMapperTest {

    private final ShortOfferMapper mapper = Mappers.getMapper(ShortOfferMapper.class);

    @Test
    @DisplayName("Маппинг Offer в ShortOfferDTO - успешный случай")
    void toShortOfferDTO_Success() {
        // Arrange
        ZonedDateTime now = ZonedDateTime.now();
        ZonedDateTime validUntil = now.plusDays(7);

        ImageDTO image = new ImageDTO(UUID.randomUUID(), "https://example.com/image.jpg", now);
        ShimpentSizeDTO size = new ShimpentSizeDTO("L", 1L, Set.of("L", "XL"));

        PurchaseOrder purchaseOrder = PurchaseOrder.builder()
                .id(100L)
                .orders(List.of(101L, 102L))
                .build();

        Shipment shipment = Shipment.builder()
                .id(10L)
                .categoryName("Одежда")
                .brandName("Nike")
                .shipmentSize(size)
                .images(List.of(image))
                .purchaseOrder(purchaseOrder)
                .build();

        Offer offer = Offer.builder()
                .id(1L)
                .status(OfferStatus.NEW)
                .validUntil(validUntil)
                .creationDate(now)
                .shipment(shipment)
                .build();

        // Act
        ShortOfferDTO result = mapper.toShortOfferDTO(offer);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.id()).isEqualTo(1L);
        assertThat(result.rootStatus().code()).isEqualTo(AggregateOfferStatus.ACTIVE.name());
        assertThat(result.rootStatus().localizedDescription()).isEqualTo(AggregateOfferStatus.ACTIVE.getDescription());
        assertThat(result.status().code()).isEqualTo(OfferStatus.NEW.name());
        assertThat(result.status().localizedDescription()).isEqualTo(OfferStatus.NEW.getDescription());
        assertThat(result.categoryName()).isEqualTo("Одежда");
        assertThat(result.brandName()).isEqualTo("Nike");
        assertThat(result.size()).isEqualTo(size);
        assertThat(result.expirationDate()).isEqualTo(validUntil);
        assertThat(result.creationDate()).isEqualTo(now);
        assertThat(result.image()).isEqualTo(image);
        assertThat(result.ordersInfo()).hasSize(2);
        assertThat(result.ordersInfo().get(0).orderId()).isEqualTo(101L);
        assertThat(result.ordersInfo().get(1).orderId()).isEqualTo(102L);
    }

    @Test
    @DisplayName("Маппинг статуса COMPLETED")
    void mapRootStatus_Completed() {
        // Arrange
        Offer offer = Offer.builder()
                .status(OfferStatus.ORDER_PAID)
                .build();

        // Act
        ShortOfferDTO result = mapper.toShortOfferDTO(offer);

        // Assert
        assertThat(result.rootStatus().code()).isEqualTo(AggregateOfferStatus.COMPLETED.name());
        assertThat(result.rootStatus().localizedDescription()).isEqualTo(AggregateOfferStatus.COMPLETED.getDescription());
    }

    @Test
    @DisplayName("Маппинг статуса ARCHIVED")
    void mapRootStatus_Archived() {
        // Arrange
        Offer offer = Offer.builder()
                .status(OfferStatus.CANCELLED_BY_EXECUTOR)
                .build();

        // Act
        ShortOfferDTO result = mapper.toShortOfferDTO(offer);

        // Assert
        assertThat(result.rootStatus().code()).isEqualTo(AggregateOfferStatus.ARCHIVED.name());
        assertThat(result.rootStatus().localizedDescription()).isEqualTo(AggregateOfferStatus.ARCHIVED.getDescription());
    }

    @Test
    @DisplayName("Маппинг с null значениями")
    void toShortOfferDTO_WithNulls() {
        // Arrange
        Shipment shipment = Shipment.builder()
                .purchaseOrder(PurchaseOrder.builder().orders(List.of()).build())
                .build();

        Offer offer = Offer.builder()
                .id(1L)
                .shipment(shipment)
                .build();

        // Act
        ShortOfferDTO result = mapper.toShortOfferDTO(offer);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.id()).isEqualTo(1L);
        assertThat(result.rootStatus().code()).isEqualTo(AggregateOfferStatus.ACTIVE.name()); // default для null status
        assertThat(result.status().code()).isEqualTo(OfferStatus.NEW.name()); // default для null status
        assertThat(result.categoryName()).isNull();
        assertThat(result.brandName()).isNull();
        assertThat(result.size()).isNull();
        assertThat(result.image()).isNull();
        assertThat(result.ordersInfo()).isEmpty();
    }

    @Test
    @DisplayName("Маппинг списка офферов")
    void toShortOfferDTOList_Success() {
        // Arrange
        Shipment shipment = Shipment.builder()
                .categoryName("Одежда")
                .purchaseOrder(PurchaseOrder.builder().orders(List.of()).build())
                .build();

        Offer offer1 = Offer.builder().id(1L).shipment(shipment).build();
        Offer offer2 = Offer.builder().id(2L).shipment(shipment).build();

        List<Offer> offers = List.of(offer1, offer2);

        // Act
        List<ShortOfferDTO> result = mapper.toShortOfferDTOList(offers);

        // Assert
        assertThat(result).hasSize(2);
        assertThat(result.get(0).id()).isEqualTo(1L);
        assertThat(result.get(1).id()).isEqualTo(2L);
        assertThat(result.get(0).categoryName()).isEqualTo("Одежда");
        assertThat(result.get(1).categoryName()).isEqualTo("Одежда");
    }
}
