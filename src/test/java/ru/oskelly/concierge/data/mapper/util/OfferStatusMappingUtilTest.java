package ru.oskelly.concierge.data.mapper.util;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferStatus;
import ru.oskelly.concierge.data.model.enums.Roles;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Тесты утилитного класса для маппинга статусов офферов")
class OfferStatusMappingUtilTest {

    @Test
    @DisplayName("Маппинг корневого статуса - ACTIVE")
    void mapRootStatus_Active() {
        Offer offer = Offer.builder()
                .status(OfferStatus.NEW)
                .build();

        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer);

        assertThat(result.code()).isEqualTo(AggregateOfferStatus.ACTIVE.name());
        assertThat(result.localizedDescription()).isEqualTo(AggregateOfferStatus.ACTIVE.getDescription());
    }

    @Test
    @DisplayName("Маппинг корневого статуса - COMPLETED")
    void mapRootStatus_Completed() {
        Offer offer = Offer.builder()
                .status(OfferStatus.ORDER_PAID)
                .build();

        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer);

        assertThat(result.code()).isEqualTo(AggregateOfferStatus.COMPLETED.name());
        assertThat(result.localizedDescription()).isEqualTo(AggregateOfferStatus.COMPLETED.getDescription());
    }

    @Test
    @DisplayName("Маппинг корневого статуса - ARCHIVED")
    void mapRootStatus_Archived() {
        Offer offer = Offer.builder()
                .status(OfferStatus.CANCELLED_BY_EXECUTOR)
                .build();

        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer);

        assertThat(result.code()).isEqualTo(AggregateOfferStatus.ARCHIVED.name());
        assertThat(result.localizedDescription()).isEqualTo(AggregateOfferStatus.ARCHIVED.getDescription());
    }

    @Test
    @DisplayName("Маппинг корневого статуса - null статус")
    void mapRootStatus_NullStatus() {
        Offer offer = Offer.builder()
                .status(null)
                .build();

        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer);

        assertThat(result.code()).isEqualTo(AggregateOfferStatus.ACTIVE.name());
        assertThat(result.localizedDescription()).isEqualTo(AggregateOfferStatus.ACTIVE.getDescription());
    }

    @Test
    @DisplayName("Преобразование OfferStatus в AggregateOfferStatus")
    void mapOfferStatusToAggregateStatus() {
        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.NEW))
                .isEqualTo(AggregateOfferStatus.ACTIVE);
        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.IN_PROGRESS))
                .isEqualTo(AggregateOfferStatus.ACTIVE);
        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.PROCESSED))
                .isEqualTo(AggregateOfferStatus.ACTIVE);
        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.AWAITING_CUSTOMER_DECISION))
                .isEqualTo(AggregateOfferStatus.ACTIVE);

        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.ORDER_PAID))
                .isEqualTo(AggregateOfferStatus.COMPLETED);

        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.CANCELLED_BY_EXECUTOR))
                .isEqualTo(AggregateOfferStatus.ARCHIVED);
        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(OfferStatus.CANCELLED_NOT_RELEVANT))
                .isEqualTo(AggregateOfferStatus.ARCHIVED);

        assertThat(OfferStatusMappingUtil.mapOfferStatusToAggregateStatus(null))
                .isEqualTo(AggregateOfferStatus.ACTIVE);
    }

    @Test
    @DisplayName("Маппинг статуса оффера")
    void mapStatus() {
        DescriptionStructureEnum result = OfferStatusMappingUtil.mapStatus(OfferStatus.NEW);
        assertThat(result.code()).isEqualTo(OfferStatus.NEW.name());
        assertThat(result.localizedDescription()).isEqualTo(OfferStatus.NEW.getDescription());

        DescriptionStructureEnum nullResult = OfferStatusMappingUtil.mapStatus(null);
        assertThat(nullResult.code()).isEqualTo(OfferStatus.NEW.name());
        assertThat(nullResult.localizedDescription()).isEqualTo(OfferStatus.NEW.getDescription());
    }

    @Test
    @DisplayName("Получение списка OfferStatus для AggregateOfferStatus")
    void getOfferStatusesForAggregateStatus() {
        List<OfferStatus> activeStatuses = OfferStatusMappingUtil.getOfferStatusesForAggregateStatus(AggregateOfferStatus.ACTIVE);
        assertThat(activeStatuses).containsExactlyInAnyOrder(
                OfferStatus.NEW,
                OfferStatus.IN_PROGRESS,
                OfferStatus.PROCESSED,
                OfferStatus.AWAITING_CUSTOMER_DECISION
        );

        List<OfferStatus> completedStatuses = OfferStatusMappingUtil.getOfferStatusesForAggregateStatus(AggregateOfferStatus.COMPLETED);
        assertThat(completedStatuses).containsExactly(OfferStatus.ORDER_PAID);

        List<OfferStatus> archivedStatuses = OfferStatusMappingUtil.getOfferStatusesForAggregateStatus(AggregateOfferStatus.ARCHIVED);
        assertThat(archivedStatuses).containsExactlyInAnyOrder(
                OfferStatus.CANCELLED_BY_EXECUTOR,
                OfferStatus.CANCELLED_NOT_RELEVANT
        );
    }

    @Test
    @DisplayName("Маппинг корневого статуса с локализацией для роли CUSTOMER")
    void mapRootStatus_WithCustomerRole() {
        Offer offer = Offer.builder()
                .status(OfferStatus.NEW)
                .build();

        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer, Roles.CUSTOMER);

        assertThat(result.code()).isEqualTo(AggregateOfferStatus.ACTIVE.name());
        assertThat(result.localizedDescription()).isEqualTo("Активные");
    }

    @Test
    @DisplayName("Маппинг статуса с локализацией для роли SALES")
    void mapStatus_WithSalesRole() {
        DescriptionStructureEnum result = OfferStatusMappingUtil.mapStatus(OfferStatus.AWAITING_CUSTOMER_DECISION, Roles.SALES);

        assertThat(result.code()).isEqualTo(OfferStatus.AWAITING_CUSTOMER_DECISION.name());
        assertThat(result.localizedDescription()).isEqualTo("Ожидаем решения клиента");
    }

    @Test
    @DisplayName("Маппинг статуса с локализацией для роли CUSTOMER")
    void mapStatus_WithCustomerRole() {
        DescriptionStructureEnum result = OfferStatusMappingUtil.mapStatus(OfferStatus.AWAITING_CUSTOMER_DECISION, Roles.CUSTOMER);

        assertThat(result.code()).isEqualTo(OfferStatus.AWAITING_CUSTOMER_DECISION.name());
        assertThat(result.localizedDescription()).isEqualTo("Ожидаем вашего решения");
    }

    @Test
    @DisplayName("Маппинг корневого статуса с локализацией для роли PERSONAL_SHOPPER")
    void mapRootStatus_WithPersonalShopperRole() {
        // Arrange
        Offer offer = Offer.builder()
                .status(OfferStatus.ORDER_PAID)
                .build();

        // Act
        DescriptionStructureEnum result = OfferStatusMappingUtil.mapRootStatus(offer, Roles.PERSONAL_SHOPPER);

        // Assert
        assertThat(result.code()).isEqualTo(AggregateOfferStatus.COMPLETED.name());
        assertThat(result.localizedDescription()).isEqualTo("Завершенные");
    }

    @Test
    @DisplayName("Маппинг статуса с локализацией для роли PERSONAL_SHOPPER")
    void mapStatus_WithPersonalShopperRole() {
        // Act
        DescriptionStructureEnum result = OfferStatusMappingUtil.mapStatus(OfferStatus.NEW, Roles.PERSONAL_SHOPPER);

        // Assert
        assertThat(result.code()).isEqualTo(OfferStatus.NEW.name());
        assertThat(result.localizedDescription()).isEqualTo("Новая заявка");
    }
}
