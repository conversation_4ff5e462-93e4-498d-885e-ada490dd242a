package ru.oskelly.concierge.data.mapper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mapstruct.factory.Mappers;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PriceInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShopperProposedOfferDTO;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.PurchaseOrder;
import ru.oskelly.concierge.data.model.Shipment;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.Currency;
import ru.oskelly.concierge.data.model.enums.OfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferType;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(SpringExtension.class)
@DisplayName("Тесты OfferDetailsMapper")
class OfferDetailsMapperTest {

    private OfferDetailsMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = Mappers.getMapper(OfferDetailsMapper.class);
    }

    @Nested
    @DisplayName("Тесты toOfferDetailsDTO")
    class ToOfferDetailsDTOTests {

        @Test
        @DisplayName("Должен успешно маппить полный Offer в OfferDetailsDTO")
        void shouldMapCompleteOfferToOfferDetailsDTO() {
            Offer offer = createCompleteOffer();

            OfferDetailsDTO result = mapper.toOfferDetailsDTO(offer);

            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.aggregateStatus().code()).isEqualTo("ACTIVE");
            assertThat(result.aggregateStatus().localizedDescription()).isEqualTo("Активные");
            assertThat(result.status()).isEqualTo(OfferStatus.IN_PROGRESS);
            assertThat(result.statusLocalized()).isEqualTo("В работе");
            assertThat(result.statusDescriptionLocalized()).isEqualTo("Заполните предложение и отправьте его менеджеру");
            assertThat(result.expirationDate()).isEqualTo(offer.getValidUntil());
            assertThat(result.creationDate()).isEqualTo(offer.getCreationDate());
            assertThat(result.categoryName()).isEqualTo("Electronics");
            assertThat(result.brandName()).isEqualTo("Apple");
            assertThat(result.materialAttributeName()).isEqualTo("Metal");
            assertThat(result.colorAttributeName()).isEqualTo("Black");
            assertThat(result.modelName()).isEqualTo("iPhone 15");
            assertThat(result.description()).isEqualTo("Test description");
            assertThat(result.ordersInfo()).hasSize(2);
            assertThat(result.proposedOffers()).hasSize(1);
        }

        @Test
        @DisplayName("Должен корректно обрабатывать null Offer")
        void shouldHandleNullOffer() {
            OfferDetailsDTO result = mapper.toOfferDetailsDTO(null);

            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Должен маппить Offer с минимальными данными")
        void shouldMapOfferWithMinimalData() {
            Offer offer = Offer.builder()
                    .id(1L)
                    .status(OfferStatus.NEW)
                    .shipment(createMinimalShipment())
                    .build();

            OfferDetailsDTO result = mapper.toOfferDetailsDTO(offer);

            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.aggregateStatus().code()).isEqualTo("ACTIVE");
            assertThat(result.aggregateStatus().localizedDescription()).isEqualTo("Активные");
            assertThat(result.status()).isEqualTo(OfferStatus.NEW);
            assertThat(result.ordersInfo()).isEmpty();
            assertThat(result.proposedOffers()).isEmpty();
        }
    }

    @Nested
    @DisplayName("Тесты mapRootStatus")
    class MapRootStatusTests {

        @Test
        @DisplayName("Должен маппить ORDER_PAID в COMPLETED")
        void shouldMapOrderPaidToCompleted() {
            Offer offer = Offer.builder().status(OfferStatus.ORDER_PAID).build();

            DescriptionStructureEnum result = mapper.mapRootStatus(offer);

            assertThat(result.code()).isEqualTo("COMPLETED");
            assertThat(result.localizedDescription()).isEqualTo("Завершенные");
        }

        @Test
        @DisplayName("Должен маппить отмененные статусы в ARCHIVED")
        void shouldMapCancelledStatusesToArchived() {
            Offer offer1 = Offer.builder().status(OfferStatus.CANCELLED_BY_EXECUTOR).build();
            Offer offer2 = Offer.builder().status(OfferStatus.CANCELLED_NOT_RELEVANT).build();

            DescriptionStructureEnum result1 = mapper.mapRootStatus(offer1);
            DescriptionStructureEnum result2 = mapper.mapRootStatus(offer2);

            assertThat(result1.code()).isEqualTo("ARCHIVED");
            assertThat(result1.localizedDescription()).isEqualTo("Архивные");
            assertThat(result2.code()).isEqualTo("ARCHIVED");
            assertThat(result2.localizedDescription()).isEqualTo("Архивные");
        }

        @Test
        @DisplayName("Должен маппить активные статусы в ACTIVE")
        void shouldMapActiveStatusesToActive() {
            Offer offer1 = Offer.builder().status(OfferStatus.NEW).build();
            Offer offer2 = Offer.builder().status(OfferStatus.IN_PROGRESS).build();
            Offer offer3 = Offer.builder().status(OfferStatus.PROCESSED).build();
            Offer offer4 = Offer.builder().status(OfferStatus.AWAITING_CUSTOMER_DECISION).build();

            DescriptionStructureEnum result1 = mapper.mapRootStatus(offer1);
            DescriptionStructureEnum result2 = mapper.mapRootStatus(offer2);
            DescriptionStructureEnum result3 = mapper.mapRootStatus(offer3);
            DescriptionStructureEnum result4 = mapper.mapRootStatus(offer4);

            assertThat(result1.code()).isEqualTo("ACTIVE");
            assertThat(result1.localizedDescription()).isEqualTo("Активные");
            assertThat(result2.code()).isEqualTo("ACTIVE");
            assertThat(result2.localizedDescription()).isEqualTo("Активные");
            assertThat(result3.code()).isEqualTo("ACTIVE");
            assertThat(result3.localizedDescription()).isEqualTo("Активные");
            assertThat(result4.code()).isEqualTo("ACTIVE");
            assertThat(result4.localizedDescription()).isEqualTo("Активные");
        }
    }

    @Nested
    @DisplayName("Тесты mapStatusLocalized")
    class MapStatusLocalizedTests {

        @Test
        @DisplayName("Должен возвращать описание для валидного статуса")
        void shouldReturnDescriptionForValidStatus() {
            String result = mapper.mapStatusLocalized(OfferStatus.IN_PROGRESS);

            assertThat(result).isEqualTo("В работе");
        }

        @Test
        @DisplayName("Должен возвращать null для null статуса")
        void shouldReturnNullForNullStatus() {
            String result = mapper.mapStatusLocalized(null);

            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("Тесты mapStatusDescriptionLocalized")
    class MapStatusDescriptionLocalizedTests {

        @Test
        @DisplayName("Должен возвращать локализованное описание для валидного статуса")
        void shouldReturnLocalizedDescriptionForValidStatus() {
            String result = mapper.mapStatusDescriptionLocalized(OfferStatus.IN_PROGRESS);

            assertThat(result).isEqualTo("Заполните предложение и отправьте его менеджеру");
        }

        @Test
        @DisplayName("Должен возвращать null для null статуса")
        void shouldReturnNullForNullStatus() {
            String result = mapper.mapStatusDescriptionLocalized(null);

            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("Тесты mapOrdersInfo")
    class MapOrdersInfoTests {

        @Test
        @DisplayName("Должен маппить ID заказов в список OrderInfoDTO")
        void shouldMapOrderIdsToOrderInfoDTOList() {
            List<Long> orderIds = List.of(1L, 2L, 3L);

            List<OrderInfoDTO> result = mapper.mapOrdersInfo(orderIds);

            assertThat(result).hasSize(3);
            assertThat(result.get(0).orderId()).isEqualTo(1L);
            assertThat(result.get(0).state()).isNull();
            assertThat(result.get(1).orderId()).isEqualTo(2L);
            assertThat(result.get(2).orderId()).isEqualTo(3L);
        }

        @Test
        @DisplayName("Должен возвращать пустой список для null заказов")
        void shouldReturnEmptyListForNullOrders() {
            List<OrderInfoDTO> result = mapper.mapOrdersInfo(null);

            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Должен возвращать пустой список для пустого списка заказов")
        void shouldReturnEmptyListForEmptyOrders() {
            List<OrderInfoDTO> result = mapper.mapOrdersInfo(List.of());

            assertThat(result).isEmpty();
        }
    }

    @Nested
    @DisplayName("Тесты mapProposedOffers")
    class MapProposedOffersTests {

        @Test
        @DisplayName("Должен маппить Set<ProposedOffer> в список ShopperProposedOfferDTO")
        void shouldMapProposedOfferSetToShopperProposedOfferDTOList() {
            Set<ProposedOffer> proposedOffers = createProposedOffers();

            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(proposedOffers);

            assertThat(result).hasSize(2);
            assertThat(result.get(0).id()).isIn(1L, 2L);
            assertThat(result.get(1).id()).isIn(1L, 2L);
        }

        @Test
        @DisplayName("Должен возвращать пустой список для null предложений")
        void shouldReturnEmptyListForNullProposedOffers() {
            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(null);

            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("Должен возвращать пустой список для пустого набора предложений")
        void shouldReturnEmptyListForEmptyProposedOffers() {
            List<ShopperProposedOfferDTO> result = mapper.mapProposedOffers(new HashSet<>());

            assertThat(result).isEmpty();
        }
    }

    @Nested
    @DisplayName("Тесты mapProposedOffer")
    class MapProposedOfferTests {

        @Test
        @DisplayName("Должен успешно маппить ProposedOffer в ShopperProposedOfferDTO")
        void shouldMapProposedOfferToShopperProposedOfferDTO() {
            ProposedOffer proposedOffer = createSingleProposedOffer();

            ShopperProposedOfferDTO result = mapper.mapProposedOffer(proposedOffer);

            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.deliveryDate()).isEqualTo(proposedOffer.getDeliveryDate());
            assertThat(result.validUntil()).isEqualTo(proposedOffer.getValidUntil());
            assertThat(result.timezone()).isEqualTo(proposedOffer.getValidUntil().getZone().getId());
            assertThat(result.hasReceipt()).isTrue();
            assertThat(result.isCompleteSet()).isTrue();
            assertThat(result.comment()).isEqualTo("Test comment");
            assertThat(result.condition()).isNotNull();
            assertThat(result.condition().id()).isEqualTo(1L);
            assertThat(result.prices()).isNotNull();
        }
    }

    @Nested
    @DisplayName("Тесты mapTimezone")
    class MapTimezoneTests {

        @Test
        @DisplayName("Должен извлекать временную зону из ZonedDateTime")
        void shouldExtractTimezoneFromZonedDateTime() {
            ZonedDateTime dateTime = ZonedDateTime.now(ZoneId.of("Europe/Moscow"));

            String result = mapper.mapTimezone(dateTime);

            assertThat(result).isEqualTo("Europe/Moscow");
        }
    }

    @Nested
    @DisplayName("Тесты mapProposedOfferStatusDescription")
    class MapProposedOfferStatusDescriptionTests {

        @Test
        @DisplayName("Должен возвращать сообщение об отправке клиенту когда isSentToCustomer равно true")
        void shouldReturnSentToCustomerMessage() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(true)
                    .build();

            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            assertThat(result).isEqualTo("Это предложение было выбрано менеджером для отправки покупателю");
        }

        @Test
        @DisplayName("Должен возвращать null когда isSentToCustomer равно false")
        void shouldReturnNullWhenNotSentToCustomer() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(false)
                    .build();

            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Должен возвращать null когда isSentToCustomer равно null")
        void shouldReturnNullWhenIsSentToCustomerIsNull() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .isSentToCustomer(null)
                    .build();

            String result = mapper.mapProposedOfferStatusDescription(proposedOffer);

            assertThat(result).isNull();
        }
    }

    @Nested
    @DisplayName("Тесты mapPriceInfo")
    class MapPriceInfoTests {

        @Test
        @DisplayName("Должен корректно рассчитывать цены с комиссией")
        void shouldCalculatePricesWithCommissionCorrectly() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(BigDecimal.valueOf(100))
                    .rublePrice(BigDecimal.valueOf(9000))
                    .commission(BigDecimal.valueOf(15))
                    .build();

            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isEqualTo(BigDecimal.valueOf(100));
            assertThat(result.rublePrice()).isEqualTo(BigDecimal.valueOf(9000));
            assertThat(result.currencyPriceWithCommission()).isEqualByComparingTo(BigDecimal.valueOf(115.00));
            assertThat(result.localPriceWithCommission()).isEqualByComparingTo(BigDecimal.valueOf(10350.00));
        }

        @Test
        @DisplayName("Должен обрабатывать null комиссию")
        void shouldHandleNullCommission() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(BigDecimal.valueOf(100))
                    .rublePrice(BigDecimal.valueOf(9000))
                    .commission(null)
                    .build();

            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isEqualTo(BigDecimal.valueOf(100));
            assertThat(result.rublePrice()).isEqualTo(BigDecimal.valueOf(9000));
            assertThat(result.currencyPriceWithCommission()).isNull();
            assertThat(result.localPriceWithCommission()).isNull();
        }

        @Test
        @DisplayName("Должен обрабатывать null цены")
        void shouldHandleNullPrices() {
            ProposedOffer proposedOffer = ProposedOffer.builder()
                    .currency(Currency.EUR)
                    .currencyPrice(null)
                    .rublePrice(null)
                    .commission(BigDecimal.valueOf(15))
                    .build();

            PriceInfoDTO result = mapper.mapPriceInfo(proposedOffer);

            assertThat(result).isNotNull();
            assertThat(result.currency()).isEqualTo(Currency.EUR);
            assertThat(result.currencyPrice()).isNull();
            assertThat(result.rublePrice()).isNull();
            assertThat(result.currencyPriceWithCommission()).isNull();
            assertThat(result.localPriceWithCommission()).isNull();
        }
    }

    @Nested
    @DisplayName("Тесты mapProductCondition")
    class MapProductConditionTests {

        @Test
        @DisplayName("Должен маппить ID состояния товара в ProductConditionDTO")
        void shouldMapProductConditionIdToProductConditionDTO() {
            Long productConditionId = 1L;

            ProductConditionDTO result = mapper.mapProductCondition(productConditionId);

            assertThat(result).isNotNull();
            assertThat(result.id()).isEqualTo(1L);
            assertThat(result.name()).isNull();
            assertThat(result.description()).isNull();
        }

        @Test
        @DisplayName("Должен возвращать null для null ID состояния товара")
        void shouldReturnNullForNullProductConditionId() {
            ProductConditionDTO result = mapper.mapProductCondition(null);

            assertThat(result).isNull();
        }
    }

    private Offer createCompleteOffer() {
        ZonedDateTime now = ZonedDateTime.now();

        ProposedOffer proposedOffer = ProposedOffer.builder()
                .id(1L)
                .rublePrice(BigDecimal.valueOf(50000))
                .currencyPrice(BigDecimal.valueOf(500))
                .currency(Currency.EUR)
                .commission(BigDecimal.valueOf(15))
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .comment("Test comment")
                .productConditionId(1L)
                .isSentToCustomer(false)
                .build();

        Set<ProposedOffer> proposedOffers = new HashSet<>();
        proposedOffers.add(proposedOffer);

        PurchaseOrder purchaseOrder = PurchaseOrder.builder()
                .orders(List.of(1L, 2L))
                .build();

        Shipment shipment = Shipment.builder()
                .id(1L)
                .categoryName("Electronics")
                .brandName("Apple")
                .materialAttributeName("Metal")
                .colorAttributeName("Black")
                .modelName("iPhone 15")
                .description("Test description")
                .images(List.of(new ImageDTO(UUID.randomUUID(), "http://example.com/image.jpg", ZonedDateTime.now())))
                .links(List.of("http://example.com"))
                .shipmentSize(new ShimpentSizeDTO("EU", 42L, Set.of("42", "43", "44")))
                .purchaseOrder(purchaseOrder)
                .build();

        return Offer.builder()
                .id(1L)
                .status(OfferStatus.IN_PROGRESS)
                .type(OfferType.BUYER_OFFER)
                .sellerId(100L)
                .validUntil(now.plusDays(30))
                .creationDate(now.minusDays(1))
                .shipment(shipment)
                .proposedOffers(proposedOffers)
                .build();
    }

    private Shipment createMinimalShipment() {
        return Shipment.builder()
                .id(1L)
                .purchaseOrder(PurchaseOrder.builder().build())
                .build();
    }

    private Set<ProposedOffer> createProposedOffers() {
        ZonedDateTime now = ZonedDateTime.now();

        ProposedOffer offer1 = ProposedOffer.builder()
                .id(1L)
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .productConditionId(1L)
                .build();

        ProposedOffer offer2 = ProposedOffer.builder()
                .id(2L)
                .deliveryDate(now.plusDays(10))
                .validUntil(now.plusDays(20))
                .hasReceipt(false)
                .isCompleteSet(false)
                .productConditionId(2L)
                .build();

        Set<ProposedOffer> offers = new HashSet<>();
        offers.add(offer1);
        offers.add(offer2);
        return offers;
    }

    private ProposedOffer createSingleProposedOffer() {
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of("Europe/Moscow"));

        return ProposedOffer.builder()
                .id(1L)
                .currency(Currency.EUR)
                .currencyPrice(BigDecimal.valueOf(100))
                .rublePrice(BigDecimal.valueOf(9000))
                .commission(BigDecimal.valueOf(15))
                .deliveryDate(now.plusDays(7))
                .validUntil(now.plusDays(14))
                .hasReceipt(true)
                .isCompleteSet(true)
                .comment("Test comment")
                .productConditionId(1L)
                .isSentToCustomer(false)
                .build();
    }
}
