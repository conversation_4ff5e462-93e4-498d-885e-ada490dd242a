package ru.oskelly.concierge.data.model.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Тесты локализации OfferStatus")
class OfferStatusTest {

    @Test
    @DisplayName("Должен возвращать описание по умолчанию для null роли")
    void shouldReturnDefaultDescriptionForNullRole() {
        String result = OfferStatus.NEW.getDescription(null);

        assertThat(result).isEqualTo("Новая заявка");
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли CUSTOMER")
    void shouldReturnLocalizedDescriptionForCustomerRole() {
        String result = OfferStatus.AWAITING_CUSTOMER_DECISION.getDescription(Roles.CUSTOMER);

        assertThat(result).isEqualTo("Ожидаем вашего решения");
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли SALES")
    void shouldReturnLocalizedDescriptionForSalesRole() {
        String result = OfferStatus.AWAITING_CUSTOMER_DECISION.getDescription(Roles.SALES);

        assertThat(result).isEqualTo("Ожидаем решения клиента");
    }

    @Test
    @DisplayName("Должен возвращать информацию о статусе по умолчанию для null роли")
    void shouldReturnDefaultStatusInfoForNullRole() {
        String result = OfferStatus.NEW.getStatusInfo(null);

        assertThat(result).isEqualTo("Для начала работы с заявкой ознакомьтесь с запросом пользователя и нажмите кнопку «Взять в работу»");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли CUSTOMER")
    void shouldReturnLocalizedStatusInfoForCustomerRole() {
        String result = OfferStatus.NEW.getStatusInfo(Roles.CUSTOMER);

        assertThat(result).isEqualTo("Заявка принята и будет обработана в ближайшее время");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли SOURCER")
    void shouldReturnLocalizedStatusInfoForSourcerRole() {
        String result = OfferStatus.IN_PROGRESS.getStatusInfo(Roles.SOURCER);

        assertThat(result).isEqualTo("Заполните предложение и отправьте его менеджеру");
    }

    @Test
    @DisplayName("Должен возвращать описание по умолчанию для несуществующей роли")
    void shouldReturnDefaultDescriptionForNonExistentRole() {
        String result = OfferStatus.NEW.getDescription(Roles.MERCAUX_ADMIN);

        assertThat(result).isEqualTo("Новая заявка"); // fallback to default
    }

    @Test
    @DisplayName("Должен возвращать localizedDescription для несуществующей роли в getStatusInfo")
    void shouldReturnLocalizedDescriptionForNonExistentRoleInStatusInfo() {
        String result = OfferStatus.NEW.getStatusInfo(Roles.MERCAUX_ADMIN);

        assertThat(result).isEqualTo("Для начала работы с заявкой ознакомьтесь с запросом пользователя и нажмите кнопку «Взять в работу»");
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли PERSONAL_SHOPPER")
    void shouldReturnLocalizedDescriptionForPersonalShopperRole() {
        // Act
        String result = OfferStatus.AWAITING_CUSTOMER_DECISION.getDescription(Roles.PERSONAL_SHOPPER);

        // Assert
        assertThat(result).isEqualTo("Ожидаем решения клиента");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли PERSONAL_SHOPPER")
    void shouldReturnLocalizedStatusInfoForPersonalShopperRole() {
        // Act
        String result = OfferStatus.NEW.getStatusInfo(Roles.PERSONAL_SHOPPER);

        // Assert
        assertThat(result).isEqualTo("Новая заявка для обработки персональным шоппером");
    }
}
