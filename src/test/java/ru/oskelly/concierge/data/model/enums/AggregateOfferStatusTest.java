package ru.oskelly.concierge.data.model.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("Тесты локализации AggregateOfferStatus")
class AggregateOfferStatusTest {

    @Test
    @DisplayName("Должен возвращать описание по умолчанию для null роли")
    void shouldReturnDefaultDescriptionForNullRole() {
        String result = AggregateOfferStatus.ACTIVE.getDescription(null);

        assertThat(result).isEqualTo("Активные");
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли CUSTOMER")
    void shouldReturnLocalizedDescriptionForCustomerRole() {
        String result = AggregateOfferStatus.ACTIVE.getDescription(Roles.CUSTOMER);

        assertThat(result).isEqualTo("Активные");
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли SALES")
    void shouldReturnLocalizedDescriptionForSalesRole() {
        String result = AggregateOfferStatus.COMPLETED.getDescription(Roles.SALES);

        assertThat(result).isEqualTo("Завершенные");
    }

    @Test
    @DisplayName("Должен возвращать информацию о статусе по умолчанию для null роли")
    void shouldReturnDefaultStatusInfoForNullRole() {
        String result = AggregateOfferStatus.ACTIVE.getStatusInfo(null);

        assertThat(result).isEqualTo("-");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли CUSTOMER")
    void shouldReturnLocalizedStatusInfoForCustomerRole() {
        String result = AggregateOfferStatus.ACTIVE.getStatusInfo(Roles.CUSTOMER);

        assertThat(result).isEqualTo("Заявки в процессе обработки");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли SOURCER")
    void shouldReturnLocalizedStatusInfoForSourcerRole() {
        String result = AggregateOfferStatus.ARCHIVED.getStatusInfo(Roles.SOURCER);

        assertThat(result).isEqualTo("Отмененные заявки");
    }

    @Test
    @DisplayName("Должен возвращать описание по умолчанию для несуществующей роли")
    void shouldReturnDefaultDescriptionForNonExistentRole() {
        String result = AggregateOfferStatus.ACTIVE.getDescription(Roles.MERCAUX_ADMIN);

        assertThat(result).isEqualTo("Активные"); // fallback to default
    }

    @Test
    @DisplayName("Должен возвращать локализованное описание для роли PERSONAL_SHOPPER")
    void shouldReturnLocalizedDescriptionForPersonalShopperRole() {
        String result = AggregateOfferStatus.COMPLETED.getDescription(Roles.PERSONAL_SHOPPER);

        assertThat(result).isEqualTo("Завершенные");
    }

    @Test
    @DisplayName("Должен возвращать локализованную информацию о статусе для роли PERSONAL_SHOPPER")
    void shouldReturnLocalizedStatusInfoForPersonalShopperRole() {
        // Act
        String result = AggregateOfferStatus.ACTIVE.getStatusInfo(Roles.PERSONAL_SHOPPER);

        // Assert
        assertThat(result).isEqualTo("Заявки в работе или требующие обработки");
    }
}
