package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Результат пагинации списка офферов шоппера
 * @param items - список офферов
 * @param itemsCount - количество элементов на текущей странице
 * @param totalPages - общее количество страниц
 * @param totalAmount - общее количество офферов
 */
@Schema(description = "Результат пагинации списка офферов шоппера")
public record PaginatedOffersResult(
        @Schema(description = "Список офферов для текущей страницы")
        @ArraySchema(schema = @Schema(implementation = ShortOfferDTO.class))
        List<ShortOfferDTO> items,

        @Schema(
                description = "Количество элементов на текущей странице",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "20",
                type = "integer",
                format = "int64"
        )
        long itemsCount,

        @Schema(
                description = "Общее количество страниц всех элементов, соответствующих критериям фильтрации",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "10",
                type = "integer",
                format = "int64"
        )
        long totalPages,

        @Schema(
                description = "Общее количество элементов, доступных по всем страницам (соответствующих критериям фильтрации)",
                requiredMode = Schema.RequiredMode.REQUIRED,
                example = "153",
                type = "integer",
                format = "int64"
        )
        long totalAmount
) {
}
