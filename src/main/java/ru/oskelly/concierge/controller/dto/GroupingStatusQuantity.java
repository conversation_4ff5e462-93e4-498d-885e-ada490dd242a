package ru.oskelly.concierge.controller.dto;

import lombok.Builder;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * DTO для группировки по статусу и количеству
 *
 * @param statusId    - статус
 * @param quantity    - количество заявок
 * @param description - описание статуса
 */
@Builder
public record GroupingStatusQuantity(
        DescriptionStructureEnum statusId,
        Long quantity,
        String description
) {
    public GroupingStatusQuantity(PurchaseOrderStatusEnum statusEnum, Long quantity) {
        this(convertToDescriptionStructure(statusEnum), quantity, getDescriptionSafely(statusEnum));
    }

    private static DescriptionStructureEnum convertToDescriptionStructure(PurchaseOrderStatusEnum statusEnum) {
        if (statusEnum == null) {
            return new DescriptionStructureEnum("UNKNOWN", "Unknown status", "unknown");
        }
        
        try {
            Roles role = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
            return new DescriptionStructureEnum(statusEnum.name(), statusEnum.getDescription(role), statusEnum.getStatusInfo(role));
        } catch (Exception e) {
            return new DescriptionStructureEnum(statusEnum.name(), statusEnum.getDescription(null), statusEnum.getStatusInfo(null));
        }
    }

    private static String getDescriptionSafely(PurchaseOrderStatusEnum statusId) {
        if (statusId == null) {
            return "Unknown status";
        }
        
        try {
            Roles roles = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
            return statusId.getDescription(roles);
        } catch (Exception e) {
            return statusId.getDescription(null);
        }
    }
}
