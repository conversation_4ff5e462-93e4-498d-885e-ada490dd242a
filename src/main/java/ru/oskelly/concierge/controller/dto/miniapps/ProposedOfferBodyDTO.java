package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;

import java.util.List;

@Schema(description = "Тело запроса для отправки предложения шоппера")
public record ProposedOfferBodyDTO(
        @Schema(description = "ID офера", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Long offerId,

        @Schema(description = "ID предложения для обновления (если null - создается новое)", example = "456")
        Long proposedOfferId,

        @Schema(description = "Список предложений", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        @Valid
        List<ProposedOfferRequestDTO> proposedOffers
) {
}
