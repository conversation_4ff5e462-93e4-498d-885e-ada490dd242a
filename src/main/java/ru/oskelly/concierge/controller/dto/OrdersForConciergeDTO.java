package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * DTO для отображения заказов консьержа
 */
@Schema(description = "DTO для отображения информации о заказах консьержа")
public record OrdersForConciergeDTO(
        @NotNull(message = "Идентификатор не может пустым")
        @Schema(
                description = "Уникальный идентификатор заказа",
                example = "12345",
                requiredMode = Schema.RequiredMode.REQUIRED
        )
        Long id,

        @Schema(description = "Описание заявки")
        String description,

        @Schema(
                description = "Информация по кастомеру",
                implementation = CustomerInfoDTO.class
        )
        CustomerInfoDTO customerInfo,

        @Schema(
                description = "Информация по менеджеру подбора предложения заказа",
                implementation = SourcerInfoDTO.class
        )
        SourcerInfoDTO sourcerInfo,

        @Schema(
                description = "Информация по менеджеру по продажам",
                implementation = SalesInfoDTO.class
        )
        SalesInfoDTO salesInfo,

        @Schema(
                description = "Источник создания заказа",
                implementation = PurchaseOrderSourceEnum.class
        )
        PurchaseOrderSourceEnum source,

        @Schema(
                description = "Дата и время создания заказа",
                example = "2023-05-15T10:30:45+03:00"
        )
        ZonedDateTime creationDate,

        @Schema(
                description = "Текущий статус заказа",
                implementation = DescriptionStructureEnum.class
        )
        DescriptionStructureEnum status,

        @Schema(
                description = "Список идентификаторов связанных заказов",
                example = "[101, 102, 201]",
                type = "array"
        )
        @ArraySchema(schema = @Schema(implementation = Long.class))
        List<Long> orders,

        @Schema(description = "Список товаров, включенных в этот заказ на покупку", type = "array")
        @ArraySchema(schema = @Schema(implementation = ShipmentResponseDTO.class))
        List<ShipmentResponseDTO> shipments,

        @Schema(
                description = "Ссылка на товар",
                example = "www.jacquemus.com"
        )
        String link,

        @Schema(description = "Список изображений, связанных с заказом")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> images
) {
        /**
         * Создает новый экземпляр OrdersForConciergeDTO с обновленным списком shipments
         * @param newShipments новый список shipments
         * @return новый экземпляр OrdersForConciergeDTO с обновленным списком shipments
         */
        public OrdersForConciergeDTO withShipments(List<ShipmentResponseDTO> newShipments) {
                return new OrdersForConciergeDTO(
                        this.id,
                        this.description,
                        this.customerInfo,
                        this.sourcerInfo,
                        this.salesInfo,
                        this.source,
                        this.creationDate,
                        this.status,
                        this.orders,
                        newShipments,
                        this.link,
                        this.images
                );
        }

        public OrdersForConciergeDTO withStatus(DescriptionStructureEnum newStatus) {
                return new OrdersForConciergeDTO(
                        this.id,
                        this.description,
                        this.customerInfo,
                        this.sourcerInfo,
                        this.salesInfo,
                        this.source,
                        this.creationDate,
                        newStatus,
                        this.orders,
                        this.shipments,
                        this.link,
                        this.images
                );
        }

        public OrdersForConciergeDTO(
                Long id,
                String description,
                CustomerInfoDTO customerInfo,
                SourcerInfoDTO sourcerInfo,
                SalesInfoDTO salesInfo,
                PurchaseOrderSourceEnum source,
                ZonedDateTime creationDate,
                DescriptionStructureEnum status,
                List<Long> orders,
                String link,
                List<ImageDTO> images
        ) {
                this(
                    id,
                    description,
                    customerInfo,
                    sourcerInfo,
                    salesInfo,
                    source,
                    creationDate,
                    status,
                    orders,
                    List.of(),
                    link,
                    images
                );
        }
}