package ru.oskelly.concierge.controller.dto;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

import java.util.List;

/**
 * DTO для ответа с информацией о товаре
 */
@Builder
@Schema(description = "DTO с информацией о товаре в заявке")
public record ShipmentResponseDTO(
        @Schema(description = "ID товара", example = "1")
        Long id,

        @Schema(description = "ID заявки, к которой относится товар", example = "100")
        Long purchaseOrderId,

        @Schema(description = "ID категории товара", example = "123")
        Long categoryId,

        @Schema(description = "Наименование категории товара", example = "Джемперы и свитеры")
        String categoryName,

        @Schema(description = "ID бренда товара", example = "456")
        Long brandId,

        @Schema(description = "Наименование бренда товара", example = "ЦВЦ СТОУНЗ")
        String brandName,

        @Schema(description = "ID атрибута материала товара", example = "789")
        Long materialAttributeId,

        @Schema(description = "Наименование атрибута материала товара", example = "Синтетика")
        String materialAttributeName,

        @Schema(description = "ID атрибута цвета товара", example = "101")
        Long colorAttributeId,

        @Schema(description = "Наименование атрибута цвета товара", example = "Оранжевый")
        String colorAttributeName,

        @Schema(description = "Дата создания записи о товаре", example = "2023-01-01T12:00:00Z")
        String createdAt,

        @Schema(description = "ID модели товара", example = "202")
        Long modelId,

        @Schema(description = "Наименование модели товара", example = "Twist")
        String modelName,

        @Schema(description = "Размеры товара")
        ShimpentSizeDTO shipmentSize,

        @Schema(description = "Описание товара", example = "Кожаная куртка черного цвета")
        String description,

        @Schema(description = "Список изображений товара")
        @ArraySchema(schema = @Schema(implementation = ImageDTO.class))
        List<ImageDTO> images,

        @Schema(description = "Ссылки на товар", example = "[\"https://example.com/product1\"]")
        @ArraySchema(schema = @Schema(implementation = String.class))
        List<String> links,

        @Schema(description = "Комментарий к товару", example = "Маленькая, бежевая")
        String comment,

        @Schema(description = "Список предложений продавцов")
        @ArraySchema(arraySchema = @Schema(implementation = OfferDTO.class))
        List<OfferDTO> offers,
        @Schema(description = "Список ID заказов, для все подчиненных заказов")
        @ArraySchema(schema = @Schema(implementation = Long.class))
        List<Long> orders
) {

    public ShipmentResponseDTO(Long id,
                               Long purchaseOrderId,
                               Long categoryId,
                               String categoryName,
                               Long brandId,
                               String brandName,
                               Long materialAttributeId,
                               String materialAttributeName,
                               Long colorAttributeId,
                               String colorAttributeName,
                               String createdAt,
                               Long modelId,
                               String modelName,
                               ShimpentSizeDTO shipmentSize,
                               String description,
                               List<ImageDTO> images,
                               List<String> links,
                               String comment) {
        this(
                id,
                purchaseOrderId,
                categoryId,
                categoryName,
                brandId,
                brandName,
                materialAttributeId,
                materialAttributeName,
                colorAttributeId,
                colorAttributeName,
                createdAt,
                modelId,
                modelName,
                shipmentSize,
                description,
                images,
                links,
                comment,
                null,
                null);
    }

    public ShipmentResponseDTO withOffers(List<OfferDTO> offers) {
        return new ShipmentResponseDTO(
                this.id,
                this.purchaseOrderId,
                this.categoryId,
                this.categoryName,
                this.brandId,
                this.brandName,
                this.materialAttributeId,
                this.materialAttributeName,
                this.colorAttributeId,
                this.materialAttributeName,
                this.createdAt,
                this.modelId,
                this.modelName,
                this.shipmentSize,
                this.description,
                this.images,
                this.links,
                this.comment,
                offers,
                this.orders
        );
    }
}
