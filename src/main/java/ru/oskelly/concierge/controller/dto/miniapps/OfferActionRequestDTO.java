package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

@Schema(description = "DTO для действий с офером (взятие в работу, отказ)")
public record OfferActionRequestDTO(
        @Schema(description = "ID офера", example = "123", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Long offerId
) {
}
