package ru.oskelly.concierge.controller.dto;

import ru.oskelly.concierge.data.model.enums.PurchaseOrderSourceEnum;

import java.time.ZonedDateTime;
import java.util.List;

public record PurchaseOrderListDTO(Long id,
                                   Long customerId,
                                   PurchaseOrderSourceEnum source,
                                   ZonedDateTime creationDate,
                                   DescriptionStructureEnum status,
                                   List<Long> orders) {}
