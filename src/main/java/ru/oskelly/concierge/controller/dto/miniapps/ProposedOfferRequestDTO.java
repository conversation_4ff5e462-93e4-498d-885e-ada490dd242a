package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;

import java.time.ZonedDateTime;

@Schema(description = "DTO для предложения шоппера")
public record ProposedOfferRequestDTO(
        @Schema(description = "Код валюты (ISO)", example = "EUR")
        String currencyCode,

        @Schema(description = "Курс валюты", example = "90.45")
        Double currencyRate,

        @Schema(description = "Цена в валюте", example = "150.00")
        Double currencyPrice,

        @Schema(description = "Цена в рублях", example = "13575.00")
        Double localPrice,

        @Schema(description = "Дата доставки", example = "2023-12-31T15:00:00+03:00")
        ZonedDateTime deliveryDate,

        @Schema(description = "ID состояния товара", example = "1")
        Integer conditionId,

        @Schema(description = "Наличие чека", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean hasReceipt,

        @Schema(description = "Полный комплект", example = "true", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull
        Boolean isCompleteSet,

        @Schema(description = "Срок действия предложения", example = "2023-12-15T23:59:59+03:00")
        ZonedDateTime validUntil,

        @Schema(description = "Временная зона", example = "Europe/Moscow")
        String timezone,

        @Schema(description = "Комментарий к предложению", example = "Товар в отличном состоянии")
        String comment
) {
}
