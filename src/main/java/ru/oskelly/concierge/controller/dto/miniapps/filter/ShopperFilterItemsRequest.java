package ru.oskelly.concierge.controller.dto.miniapps.filter;

import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * Запрос с параметрами фильтрации для списка шопперов
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Запрос для фильтрации элементов покупателя с поддержкой пагинации и фильтров")
public class ShopperFilterItemsRequest {

    @Schema(description = "Номер страницы для пагинации", example = "1")
    private Integer page;

    @Schema(description = "Количество элементов на странице", example = "10")
    private Integer pageLength;

    @Schema(description = "Набор фильтров в формате ключ-значение",
    example = """
              "filters": {
                "brand": [
                  815,
                  1995,
                  1996,
                  2033,
                  675
                ],
                "category": [
                  20
                ]
              }
            """)
    private Map<String, JsonNode> filters;

    @Schema(description = "Набор предустановленных фильтров в формате ключ-значение",
            example = """
                    "presets": {
                        "rootStatus": "ARCHIVE",
                        "status": "IN_WORK"
                      }
                    """)
    private Map<String, JsonNode> presets;

    public ShopperFilterItemsRequest(ShopperFilterItemsRequest request) {
        this.page = request.getPage();
        this.pageLength = request.getPageLength();
        this.filters = request.getFilters();
        this.presets = request.getPresets();
    }
}
