package ru.oskelly.concierge.controller.dto.miniapps;

import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.ShimpentSizeDTO;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferStatus;

import java.time.ZonedDateTime;
import java.util.List;

/**
 * DTO для краткой информации об оффере шоппера
 */
@Schema(description = "Краткая информация об оффере для шоппера")
public record ShortOfferDTO(
        @Schema(description = "Уникальный идентификатор оффера", example = "123")
        Long id,

        @Schema(description = "Корневой статус оффера (Активные, Завершенные, Архив)", implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum rootStatus,

        @Schema(description = "Текущий статус оффера", implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum status,

        @Schema(description = "Название категории товара", example = "Одежда")
        String categoryName,

        @Schema(description = "Название бренда", example = "Nike")
        String brandName,

        @Schema(description = "Размер товара", implementation = ShimpentSizeDTO.class)
        ShimpentSizeDTO size,

        @Schema(description = "Дата истечения срока действия оффера", example = "2023-12-31T23:59:59+03:00")
        ZonedDateTime expirationDate,

        @Schema(description = "Дата создания оффера (время отправки офера шопперу)", example = "2023-11-25T15:30:00+03:00")
        ZonedDateTime creationDate,

        @Schema(description = "Изображение товара (показываем только одно, но можно прислать и все)", implementation = ImageDTO.class)
        ImageDTO image,

        @Schema(description = "Информация о связанных заказах")
        @ArraySchema(schema = @Schema(implementation = OrderInfoDTO.class))
        List<OrderInfoDTO> ordersInfo
) {
}
