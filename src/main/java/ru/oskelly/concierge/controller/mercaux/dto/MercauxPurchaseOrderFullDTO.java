package ru.oskelly.concierge.controller.mercaux.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import ru.oskelly.concierge.controller.dto.CommentFullDTO;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.SalesInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.CustomerInfoDTO;
import ru.oskelly.concierge.controller.dto.managerdto.SourcerInfoDTO;

import java.time.ZonedDateTime;
import java.util.List;

@Schema(
        description = "Полные данные о заказе на покупку для системы Mercaux",
        name = "MercauxPurchaseOrderFullDTO",
        title = "Заказ на покупку Mercaux"
)
@JsonInclude(JsonInclude.Include.NON_NULL)
public record MercauxPurchaseOrderFullDTO(
        @Schema(description = "Уникальный идентификатор заказа", example = "12345")
        Long id,

        @Schema(description = "ID клиента, оформившего заказ", implementation = CustomerInfoDTO.class)
        CustomerInfoDTO customer,

        @Schema(description = "Источник, откуда поступил заказ",
                implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum source,

        @Schema(description = "Описание заказа", example = "Канцтовары для офиса на 3 квартал")
        String description,

        @Schema(description = "Дата и время создания заказа", example = "2023-07-15T10:30:45Z")
        ZonedDateTime creationDate,

        @Schema(description = "Дата и время последнего изменения заказа", example = "2023-07-16T14:25:30Z")
        ZonedDateTime changeDate,

        @Schema(description = "Текущий статус заказа",
                implementation = DescriptionStructureEnum.class)
        DescriptionStructureEnum status,

        @Schema(
                description = "Информация по менеджеру подбора предложения заказа",
                implementation = SourcerInfoDTO.class
        )
        SourcerInfoDTO sourcerInfo,

        @Schema(
                description = "Информация по менеджеру по продажам",
                implementation = SalesInfoDTO.class
        )
        SalesInfoDTO salesInfo,

        @Schema(description = "Список изображений, связанных с заказом")
        @ArraySchema(schema = @Schema(implementation =  ImageDTO.class))
        List<ImageDTO> images,

        @Schema(description = "Список ID заказов, включенных в этот заказ на покупку")
        @ArraySchema(schema = @Schema(implementation = Long.class))
        List<Long> orders,

        @Schema(description = "Список комментариев к этому заказу")
        @ArraySchema(schema = @Schema(implementation = CommentFullDTO.class))
        List<CommentFullDTO> comments,

        @Schema(description = "Причина отказа в заказе, если применимо", example = "Нет в наличии")
        String rejectionReason,

        @Schema(description = "Подробное описание причины отказа, если применимо",
                example = "Запрашиваемые товары в настоящее время отсутствуют на складе и будут доступны только в следующем месяце")
        String rejectionDescription,

        @Schema(description = "Список товаров, включенных в этот заказ на покупку для Mercaux")
        @ArraySchema(schema = @Schema(implementation = MercauxShipmentResponseDTO.class))
        List<MercauxShipmentResponseDTO> shipments,

        @Schema(description = "Ссылка на товар", example = "www.jacquemus.com")
        String link
) {
}
