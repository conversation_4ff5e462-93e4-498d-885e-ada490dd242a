package ru.oskelly.concierge.controller.miniapps;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import ru.oskelly.concierge.controller.dto.miniapps.OfferActionRequestDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ProposedOfferBodyDTO;

@Validated
@Tag(name = "offer-shopper-controller", description = "API для работы шоппера с предложениями")
@RequestMapping("/api/v1/shopper/offers")
public interface OfferShopperApiDelegate {

    @Operation(
            summary = "Отправка готового предложения",
            description = "Отправка готового предложения с валидацией и сменой статуса на PROCESSED",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Предложение успешно отправлено"),
                    @ApiResponse(responseCode = "400", description = "Ошибка валидации данных"),
                    @ApiResponse(responseCode = "404", description = "Офер не найден")
            }
    )
    @PostMapping(
            value = "/submit",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<Void> submitProposedOffer(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные готового предложения",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ProposedOfferBodyDTO.class)))
            @RequestBody @Valid ProposedOfferBodyDTO proposedOfferBody
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Сохранение черновика предложения",
            description = "Сохранение черновика предложения без валидации полей и смены статуса",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Черновик успешно сохранен"),
                    @ApiResponse(responseCode = "404", description = "Офер не найден")
            }
    )
    @PostMapping(
            value = "/draft",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<Void> saveDraftProposedOffer(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Данные черновика предложения",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ProposedOfferBodyDTO.class)))
            @RequestBody @Valid ProposedOfferBodyDTO proposedOfferBody
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Отказ от офера",
            description = "Отклонение офера шоппером со сменой статуса на CANCELLED_BY_EXECUTOR",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Офер успешно отклонен"),
                    @ApiResponse(responseCode = "404", description = "Офер не найден")
            }
    )
    @PostMapping(
            value = "/reject",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<Void> rejectOffer(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "ID офера для отклонения",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = OfferActionRequestDTO.class)))
            @RequestBody @Valid OfferActionRequestDTO request
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }

    @Operation(
            summary = "Взятие офера в работу",
            description = "Взятие офера в работу шоппером со сменой статуса на IN_PROGRESS",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Офер успешно взят в работу"),
                    @ApiResponse(responseCode = "404", description = "Офер не найден"),
                    @ApiResponse(responseCode = "409", description = "Офер уже в работе")
            }
    )
    @PostMapping(
            value = "/take",
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    default ResponseEntity<Void> takeOfferInProgress(
            @Parameter(name = "userId", description = "ID пользователя", required = true, in = ParameterIn.QUERY)
            @RequestParam(name = "userId") Long userId,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "ID офера для взятия в работу",
                    required = true,
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = OfferActionRequestDTO.class)))
            @RequestBody @Valid OfferActionRequestDTO request
    ) {
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);
    }
}
