package ru.oskelly.concierge.controller.miniapps;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.miniapps.OfferActionRequestDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ProposedOfferBodyDTO;
import ru.oskelly.concierge.service.OfferService;

@RestController
@RequiredArgsConstructor
public class OfferShopperController implements OfferShopperApiDelegate {

    private final OfferService offerService;

    @Override
    public ResponseEntity<Void> submitProposedOffer(Long userId, ProposedOfferBodyDTO proposedOfferBody) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        offerService.submitProposedOffer(proposedOfferBody);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> saveDraftProposedOffer(Long userId, ProposedOfferBodyDTO proposedOfferBody) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        offerService.saveDraftProposedOffer(proposedOfferBody);
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> rejectOffer(Long userId, OfferActionRequestDTO request) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        offerService.rejectOffer(request.offerId());
        return ResponseEntity.ok().build();
    }

    @Override
    public ResponseEntity<Void> takeOfferInProgress(Long userId, OfferActionRequestDTO request) {
        ThreadLocalContext.put(ContextConstants.USER_ID, userId);
        offerService.takeOfferInProgress(request.offerId());
        return ResponseEntity.ok().build();
    }
}
