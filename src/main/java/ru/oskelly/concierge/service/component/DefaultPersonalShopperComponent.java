package ru.oskelly.concierge.service.component;

import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.PaginatedShoppersResult;
import ru.oskelly.concierge.controller.dto.PersonalShopperDTO;
import ru.oskelly.concierge.controller.dto.PersonalShopperFilter;
import ru.oskelly.concierge.data.model.PersonalShopper;
import ru.oskelly.concierge.data.model.QOffer;
import ru.oskelly.concierge.data.model.QPersonalShopper;
import ru.oskelly.concierge.data.model.QPurchaseOrder;
import ru.oskelly.concierge.data.model.enums.AccessSource;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.ShopperCategoryEnum;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class DefaultPersonalShopperComponent implements PersonalShopperComponent {
    private final JPAQueryFactory queryFactory;

    @Override
    public PersonalShopperFilter filterPersonalShopper() {
        List<DescriptionStructureEnum> accessSources = Arrays.stream(AccessSource.values())
                .map(accessSource -> new DescriptionStructureEnum(
                        accessSource.name(),
                        accessSource.getDescription(),
                        "-"
                ))
                .toList();

        List<DescriptionStructureEnum> categories = Arrays.stream(ShopperCategoryEnum.values())
                .map(shopperCategory -> new DescriptionStructureEnum(
                        shopperCategory.name(),
                        shopperCategory.getDescription(),
                        "-"
                ))
                .toList();

        return new PersonalShopperFilter(accessSources, categories, Set.of(), 0, 20);
    }

    @Override
    public PaginatedShoppersResult getPersonalShoppers(PersonalShopperFilter filter, String searchText) {
        QPersonalShopper personalShopper = QPersonalShopper.personalShopper;
        QOffer offer = QOffer.offer;
        QPurchaseOrder purchaseOrder = QPurchaseOrder.purchaseOrder;

        BooleanExpression whereCondition = buildFilterCondition(personalShopper, filter, searchText);

        long totalCount = getTotalCount(personalShopper, whereCondition);

        if (totalCount == 0) {
            return new PaginatedShoppersResult(List.of(), 0, 0, 0);
        }

        int page = filter != null && filter.page() != null ? filter.page() - 1 : 0;
        int pageSize = filter != null && filter.pageSize() != null ? filter.pageSize() : 20;
        int totalPages = (int) Math.ceil((double) totalCount / pageSize);

        List<PersonalShopper> shoppers = fetchShoppers(personalShopper, whereCondition, page, pageSize);

        Map<Long, Long> doneOrdersCountMap = fetchDoneOrdersCount(offer, purchaseOrder, shoppers);

        List<PersonalShopperDTO> resultList = mapToDto(shoppers, doneOrdersCountMap);

        return new PaginatedShoppersResult(resultList, resultList.size(), totalPages, totalCount);
    }

    // Метод для построения условий фильтрации
    private BooleanExpression buildFilterCondition(QPersonalShopper personalShopper,
                                                   PersonalShopperFilter filter,
                                                   String searchText) {
        BooleanExpression whereCondition = personalShopper.id.isNotNull();

        if (filter != null) {
            // Фильтрация по accessSources
            if (filter.accessSources() != null && !filter.accessSources().isEmpty()) {
                List<AccessSource> validSources = filter.accessSources().stream()
                        .filter(Objects::nonNull)
                        .map(descriptionStructureEnum -> AccessSource.valueOf(descriptionStructureEnum.code()))
                        .toList();

                if (!validSources.isEmpty()) {
                    whereCondition = whereCondition.and(personalShopper.accessSource.in(validSources));
                }
            }

            whereCondition = whereCondition.and(filterCategoryAndBrand(filter));
        }

        // Фильтрация по поисковому тексту (ID, ФИО, никнейм)
        if (searchText != null && !searchText.isBlank()) {
            String searchPattern = "%" + searchText.toLowerCase() + "%";
            BooleanExpression textSearchCondition = personalShopper.id.stringValue().lower().like(searchPattern)
                    .or(personalShopper.name.lower().like(searchPattern))
                    .or(personalShopper.nickname.lower().like(searchPattern));
            whereCondition = whereCondition.and(textSearchCondition);
        }

        return whereCondition;
    }

    private BooleanExpression filterCategoryAndBrand(PersonalShopperFilter filter) {
        QPersonalShopper ps = QPersonalShopper.personalShopper;
        BooleanExpression categoryPredicate = null;
        BooleanExpression brandPredicate = null;
        Set<ShopperCategoryEnum> categoryEnums = filter.categories().stream()
                .map(descriptionStructureEnum -> ShopperCategoryEnum.valueOf(descriptionStructureEnum.code()))
                .collect(Collectors.toSet());

        if (!categoryEnums.isEmpty()) {
            categoryPredicate = ps.categories.any().code.in(categoryEnums);
        }

        if (filter.brandIds() != null && !filter.brandIds().isEmpty()) {
            brandPredicate = ps.brands.any().brandId.in(filter.brandIds());
        }

        return Expressions.anyOf(categoryPredicate, brandPredicate);
    }

    // Метод для получения общего количества записей
    private long getTotalCount(QPersonalShopper personalShopper, BooleanExpression whereCondition) {
        return queryFactory.selectFrom(personalShopper)
                .where(whereCondition).fetch().size();
    }

    private List<PersonalShopper> fetchShoppers(QPersonalShopper personalShopper,
                                                BooleanExpression whereCondition,
                                                int page,
                                                int pageSize) {
        return queryFactory.selectFrom(personalShopper)
                .where(whereCondition)
                .orderBy(personalShopper.id.asc())
                .offset((long) page * pageSize)
                .limit(pageSize)
                .fetch();
    }

    private Map<Long, Long> fetchDoneOrdersCount(QOffer offer,
                                                 QPurchaseOrder purchaseOrder,
                                                 List<PersonalShopper> shoppers) {
        if (shoppers.isEmpty()) {
            return Collections.emptyMap();
        }

        List<Long> userIds = shoppers.stream()
                .map(PersonalShopper::getUserId)
                .filter(Objects::nonNull)
                .toList();

        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return queryFactory
                .select(offer.userId, purchaseOrder.count())
                .from(offer)
                .join(offer.shipment.purchaseOrder, purchaseOrder)
                .where(offer.userId.in(userIds)
                        .and(purchaseOrder.status.eq(PurchaseOrderStatusEnum.DONE)))
                .groupBy(offer.userId)
                .fetch()
                .stream()
                .filter(tuple -> tuple.get(offer.userId) != null) // Фильтруем записи с null ключами
                .collect(Collectors.toMap(
                        tuple -> tuple.get(offer.userId),
                        tuple -> Optional.ofNullable(tuple.get(purchaseOrder.count())).orElse(0L) // Защита от null значений
                ));
    }

    // Метод для преобразования данных в DTO
    private List<PersonalShopperDTO> mapToDto(List<PersonalShopper> shoppers,
                                              Map<Long, Long> doneOrdersCountMap) {
        return shoppers.stream()
                .map(shopper -> new PersonalShopperDTO(
                        shopper.getId(),
                        shopper.getUserId(),
                        null, // urlAvatar - будет реализовано позже
                        shopper.getName(),
                        shopper.getNickname(),
                        shopper.getPaymentFormat() != null ? new DescriptionStructureEnum(
                                shopper.getPaymentFormat().name(),
                                shopper.getPaymentFormat().getDescription(),
                                "-"
                        ) : null,
                        shopper.getInteractionType().stream()
                                .map(it -> new DescriptionStructureEnum(it.name(), it.getValue(), "-"))
                                .collect(Collectors.toSet()),
                        doneOrdersCountMap.getOrDefault(shopper.getUserId(), 0L)
                ))
                .toList();
    }
}
