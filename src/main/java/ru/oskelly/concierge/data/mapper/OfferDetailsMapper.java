package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ProductConditionDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShopperProposedOfferDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OfferDetailsDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.PriceInfoDTO;
import ru.oskelly.concierge.data.mapper.util.OfferStatusMappingUtil;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.enums.OfferStatus;
import ru.oskelly.concierge.data.model.enums.Roles;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface OfferDetailsMapper {

    @Mapping(target = "id", source = "offer.id")
    @Mapping(target = "aggregateStatus", source = "offer", qualifiedByName = "mapRootStatus")
    @Mapping(target = "status", source = "offer.status")
    @Mapping(target = "statusLocalized", source = "offer.status", qualifiedByName = "mapStatusLocalized")
    @Mapping(target = "statusDescriptionLocalized", source = "offer.status", qualifiedByName = "mapStatusDescriptionLocalized")
    @Mapping(target = "expirationDate", source = "offer.validUntil")
    @Mapping(target = "ordersInfo", source = "offer.shipment.purchaseOrder.orders", qualifiedByName = "mapOrdersInfo")
    @Mapping(target = "proposedOffers", source = "offer.proposedOffers", qualifiedByName = "mapProposedOffers")
    @Mapping(target = "creationDate", source = "offer.creationDate")
    @Mapping(target = "categoryName", source = "offer.shipment.categoryName")
    @Mapping(target = "brandName", source = "offer.shipment.brandName")
    @Mapping(target = "materialAttributeName", source = "offer.shipment.materialAttributeName")
    @Mapping(target = "colorAttributeName", source = "offer.shipment.colorAttributeName")
    @Mapping(target = "modelName", source = "offer.shipment.modelName")
    @Mapping(target = "size", source = "offer.shipment.shipmentSize")
    @Mapping(target = "description", source = "offer.shipment.description")
    @Mapping(target = "images", source = "offer.shipment.images")
    @Mapping(target = "links", source = "offer.shipment.links")
    OfferDetailsDTO toOfferDetailsDTO(Offer offer);

    @Named("mapRootStatus")
    default DescriptionStructureEnum mapRootStatus(Offer offer) {
        return OfferStatusMappingUtil.mapRootStatus(offer);
    }

    @Named("mapStatusLocalized")
    default String mapStatusLocalized(OfferStatus status) {
        Roles role = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
        return status != null ? status.getDescription(role) : null;
    }

    @Named("mapStatusDescriptionLocalized")
    default String mapStatusDescriptionLocalized(OfferStatus status) {
        Roles role = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
        return status != null ? status.getStatusInfo(role) : null;
    }

    @Named("mapOrdersInfo")
    default List<OrderInfoDTO> mapOrdersInfo(List<Long> orders) {
        if (orders == null || orders.isEmpty()) {
            return List.of();
        }
        return orders.stream()
                .map(orderId -> new OrderInfoDTO(orderId, null))
                .toList();
    }

    @Named("mapProposedOffers")
    default List<ShopperProposedOfferDTO> mapProposedOffers(Set<ProposedOffer> proposedOffers) {
        if (proposedOffers == null || proposedOffers.isEmpty()) {
            return List.of();
        }
        return proposedOffers.stream()
                .map(this::mapProposedOffer)
                .toList();
    }

    @Mapping(target = "id", source = "id")
    @Mapping(target = "statusDescription", source = "proposedOffer", qualifiedByName = "mapProposedOfferStatusDescription")
    @Mapping(target = "deliveryDate", source = "deliveryDate")
    @Mapping(target = "validUntil", source = "validUntil")
    @Mapping(target = "timezone", source = "validUntil", qualifiedByName = "mapTimezone")
    @Mapping(target = "prices", source = "proposedOffer", qualifiedByName = "mapPriceInfo")
    @Mapping(target = "condition", source = "productConditionId", qualifiedByName = "mapProductCondition")
    @Mapping(target = "hasReceipt", source = "hasReceipt")
    @Mapping(target = "isCompleteSet", source = "completeSet")
    @Mapping(target = "comment", source = "comment")
    ShopperProposedOfferDTO mapProposedOffer(ProposedOffer proposedOffer);

    @Named("mapTimezone")
    default String mapTimezone(ZonedDateTime validUntil) {
        return validUntil.getZone().getId();
    }

    @Named("mapProposedOfferStatusDescription")
    default String mapProposedOfferStatusDescription(ProposedOffer proposedOffer) {
        if (Boolean.TRUE.equals(proposedOffer.getIsSentToCustomer())) {
            return "Это предложение было выбрано менеджером для отправки покупателю";
        }
        // ToDo раскоментировать, когда приедет (161)
//        if (proposedOffer.getOrders() != null && !proposedOffer.getOrders().isEmpty()) {
//            return "По данному предложению был сформирован и оплачен заказ";
//        }
        return null;
    }

    @Named("mapPriceInfo")
    default PriceInfoDTO mapPriceInfo(ProposedOffer proposedOffer) {
        BigDecimal currencyPrice = proposedOffer.getCurrencyPrice();
        BigDecimal rublePrice = proposedOffer.getRublePrice();
        BigDecimal commission = proposedOffer.getCommission();
        
        BigDecimal currencyPriceWithCommission = null;
        BigDecimal localPriceWithCommission = null;
        
        if (currencyPrice != null && commission != null) {
            BigDecimal commissionMultiplier = BigDecimal.ONE.add(commission.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            currencyPriceWithCommission = currencyPrice.multiply(commissionMultiplier).setScale(2, RoundingMode.HALF_UP);
        }
        
        if (rublePrice != null && commission != null) {
            BigDecimal commissionMultiplier = BigDecimal.ONE.add(commission.divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP));
            localPriceWithCommission = rublePrice.multiply(commissionMultiplier).setScale(2, RoundingMode.HALF_UP);
        }
        
        return new PriceInfoDTO(
                proposedOffer.getCurrency(),
                currencyPrice,
                rublePrice,
                currencyPriceWithCommission,
                localPriceWithCommission
        );
    }

    @Named("mapProductCondition")
    default ProductConditionDTO mapProductCondition(Long productConditionId) {
        if (productConditionId == null) return null;
        return new ProductConditionDTO(productConditionId, null, null);
    }
}
