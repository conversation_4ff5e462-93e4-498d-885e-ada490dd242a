package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.ImageDTO;
import ru.oskelly.concierge.controller.dto.miniapps.OrderInfoDTO;
import ru.oskelly.concierge.controller.dto.miniapps.ShortOfferDTO;
import ru.oskelly.concierge.data.mapper.util.OfferStatusMappingUtil;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.enums.OfferStatus;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Маппер для преобразования Offer в ShortOfferDTO
 */
@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface ShortOfferMapper {

    @Mapping(target = "id", source = "offer.id")
    @Mapping(target = "rootStatus", source = "offer", qualifiedByName = "mapRootStatus")
    @Mapping(target = "status", source = "offer.status", qualifiedByName = "mapStatus")
    @Mapping(target = "categoryName", source = "offer.shipment.categoryName")
    @Mapping(target = "brandName", source = "offer.shipment.brandName")
    @Mapping(target = "size", source = "offer.shipment.shipmentSize")
    @Mapping(target = "expirationDate", source = "offer.validUntil")
    @Mapping(target = "creationDate", source = "offer.creationDate")
    @Mapping(target = "image", source = "offer.shipment.images", qualifiedByName = "mapFirstImage")
    @Mapping(target = "ordersInfo", source = "offer.shipment.purchaseOrder.orders", qualifiedByName = "mapOrdersInfo")
    ShortOfferDTO toShortOfferDTO(Offer offer);

    /**
     * Преобразует список офферов в список ShortOfferDTO
     */
    List<ShortOfferDTO> toShortOfferDTOList(List<Offer> offers);

    /**
     * Маппинг корневого статуса (агрегированного)
     */
    @Named("mapRootStatus")
    default DescriptionStructureEnum mapRootStatus(Offer offer) {
        return OfferStatusMappingUtil.mapRootStatus(offer);
    }

    /**
     * Маппинг статуса оффера
     */
    @Named("mapStatus")
    default DescriptionStructureEnum mapStatus(OfferStatus status) {
        return OfferStatusMappingUtil.mapStatus(status);
    }

    /**
     * Получает первое изображение из списка
     */
    @Named("mapFirstImage")
    default ImageDTO mapFirstImage(List<ImageDTO> images) {
        if (images == null || images.isEmpty()) {
            return null;
        }
        return images.get(0);
    }

    /**
     * Преобразует список ID заказов в OrderInfoDTO
     */
    @Named("mapOrdersInfo")
    default List<OrderInfoDTO> mapOrdersInfo(List<Long> orderIds) {
        if (orderIds == null || orderIds.isEmpty()) {
            return List.of();
        }

        return orderIds.stream()
                .map(orderId -> new OrderInfoDTO(orderId, "UNKNOWN")) // Статус заказа пока неизвестен
                .collect(Collectors.toList());
    }
}
