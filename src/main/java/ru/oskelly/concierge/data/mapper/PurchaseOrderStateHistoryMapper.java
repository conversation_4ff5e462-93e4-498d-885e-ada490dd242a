package ru.oskelly.concierge.data.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import ru.oskelly.concierge.common.ContextConstants;
import ru.oskelly.concierge.common.ThreadLocalContext;
import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.controller.dto.PurchaseOrderStateHistoryDTO;
import ru.oskelly.concierge.data.model.PurchaseOrderStateHistory;
import ru.oskelly.concierge.data.model.enums.PurchaseOrderStatusEnum;
import ru.oskelly.concierge.data.model.enums.Roles;

/**
 * Mapper для работы с историей состояний заявок на покупку
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PurchaseOrderStateHistoryMapper {
    @Mapping(target = "sourceState", source = "sourceState", qualifiedByName = "descriptionStructureToStatus")
    @Mapping(target = "targetState", source = "targetState", qualifiedByName = "descriptionStructureToStatus")
    PurchaseOrderStateHistory toEntity(PurchaseOrderStateHistoryDTO purchaseOrderStateHistoryDTO);

    @Mapping(target = "sourceState", source = "sourceState", qualifiedByName = "statusToDescriptionStructure")
    @Mapping(target = "targetState", source = "targetState", qualifiedByName = "statusToDescriptionStructure")
    PurchaseOrderStateHistoryDTO toDto(PurchaseOrderStateHistory purchaseOrderStateHistory);

    @Named("statusToDescriptionStructure")
    default DescriptionStructureEnum statusToDescriptionStructure(PurchaseOrderStatusEnum status) {
        if (status == null) {
            return null;
        }
        
        try {
            Roles roles = ThreadLocalContext.get(ContextConstants.ROLE, Roles.class);
            return new DescriptionStructureEnum(status.name(), status.getDescription(roles), status.getStatusInfo(roles));
        } catch (Exception e) {
            return new DescriptionStructureEnum(status.name(), status.getDescription(null), status.getStatusInfo(null));
        }
    }

    @Named("descriptionStructureToStatus")
    default PurchaseOrderStatusEnum descriptionStructureToStatus(DescriptionStructureEnum descriptionStructure) {
        if (descriptionStructure == null || descriptionStructure.code() == null) {
            return null;
        }
        
        try {
            return PurchaseOrderStatusEnum.valueOf(descriptionStructure.code());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
}
