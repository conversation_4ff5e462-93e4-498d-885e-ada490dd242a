package ru.oskelly.concierge.data.mapper.util;

import ru.oskelly.concierge.controller.dto.DescriptionStructureEnum;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.enums.AggregateOfferStatus;
import ru.oskelly.concierge.data.model.enums.OfferStatus;
import ru.oskelly.concierge.data.model.enums.Roles;

import java.util.List;

/**
 * Утилитный класс для маппинга статусов офферов
 */
public final class OfferStatusMappingUtil {

    private OfferStatusMappingUtil() {
    }

    /**
     * Преобразует статус оффера в агрегированный статус с описанием
     *
     * @param offer оффер для получения статуса
     * @return DescriptionStructureEnum с агрегированным статусом
     */
    public static DescriptionStructureEnum mapRootStatus(Offer offer) {
        return mapRootStatus(offer, null);
    }

    /**
     * Преобразует статус оффера в агрегированный статус с описанием для указанной роли
     *
     * @param offer оффер для получения статуса
     * @param role роль пользователя для локализации
     * @return DescriptionStructureEnum с агрегированным статусом
     */
    public static DescriptionStructureEnum mapRootStatus(Offer offer, Roles role) {
        var status = offer.getStatus();
        if (status == null) {
            return new DescriptionStructureEnum(
                    AggregateOfferStatus.ACTIVE.name(),
                    AggregateOfferStatus.ACTIVE.getDescription(role),
                    AggregateOfferStatus.ACTIVE.getStatusInfo(role)
            );
        }

        AggregateOfferStatus aggregateStatus = mapOfferStatusToAggregateStatus(status);

        return new DescriptionStructureEnum(
                aggregateStatus.name(),
                aggregateStatus.getDescription(role),
                aggregateStatus.getStatusInfo(role)
        );
    }

    /**
     * Преобразует OfferStatus в AggregateOfferStatus
     *
     * @param status статус оффера
     * @return агрегированный статус
     */
    public static AggregateOfferStatus mapOfferStatusToAggregateStatus(OfferStatus status) {
        if (status == null) {
            return AggregateOfferStatus.ACTIVE;
        }

        return switch (status) {
            case ORDER_PAID -> AggregateOfferStatus.COMPLETED;
            case CANCELLED_BY_EXECUTOR, CANCELLED_NOT_RELEVANT -> AggregateOfferStatus.ARCHIVED;
            case NEW, IN_PROGRESS, PROCESSED, AWAITING_CUSTOMER_DECISION -> AggregateOfferStatus.ACTIVE;
        };
    }

    /**
     * Преобразует статус оффера в DescriptionStructureEnum
     *
     * @param status статус оффера
     * @return DescriptionStructureEnum со статусом
     */
    public static DescriptionStructureEnum mapStatus(OfferStatus status) {
        return mapStatus(status, null);
    }

    /**
     * Преобразует статус оффера в DescriptionStructureEnum для указанной роли
     *
     * @param status статус оффера
     * @param role роль пользователя для локализации
     * @return DescriptionStructureEnum со статусом
     */
    public static DescriptionStructureEnum mapStatus(OfferStatus status, Roles role) {
        if (status == null) {
            return new DescriptionStructureEnum(
                    OfferStatus.NEW.name(),
                    OfferStatus.NEW.getDescription(role),
                    OfferStatus.NEW.getStatusInfo(role)
            );
        }

        return new DescriptionStructureEnum(
                status.name(),
                status.getDescription(role),
                status.getStatusInfo(role)
        );
    }

    /**
     * Преобразует AggregateOfferStatus в список соответствующих OfferStatus
     *
     * @param aggregateStatus агрегированный статус
     * @return список соответствующих статусов офферов
     */
    public static List<OfferStatus> getOfferStatusesForAggregateStatus(AggregateOfferStatus aggregateStatus) {
        return switch (aggregateStatus) {
            case ACTIVE -> List.of(OfferStatus.NEW, OfferStatus.IN_PROGRESS, OfferStatus.PROCESSED, OfferStatus.AWAITING_CUSTOMER_DECISION);
            case COMPLETED -> List.of(OfferStatus.ORDER_PAID);
            case ARCHIVED -> List.of(OfferStatus.CANCELLED_BY_EXECUTOR, OfferStatus.CANCELLED_NOT_RELEVANT);
        };
    }
}
