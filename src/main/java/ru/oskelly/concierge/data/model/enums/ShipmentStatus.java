package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "Статусы товара в заказе на покупку")
public enum ShipmentStatus {
    @Schema(description = "Товар ожидает обработки")
    AWAITING_PROCESSING("Ожидает обработки"),

    @Schema(description = "Товар в работе")
    IN_PROGRESS("В работе"),

    @Schema(description = "Товар готов")
    READY("Готов"),

    @Schema(description = "Товар отклонен")
    REJECTED("Отклонен");

    private final String description;

    ShipmentStatus(String description) {
        this.description = description;
    }
}
