package ru.oskelly.concierge.data.model;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.SequenceGenerator;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import ru.oskelly.concierge.data.model.converter.InteractionTypeListConverter;
import ru.oskelly.concierge.data.model.enums.AccessSource;
import ru.oskelly.concierge.data.model.enums.InteractionType;
import ru.oskelly.concierge.data.model.enums.PaymentFormat;

import java.time.ZonedDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Table(name = "personal_shopper")
public class PersonalShopper {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "personal_shopper_generator")
    @SequenceGenerator(name = "personal_shopper_generator", sequenceName = "personal_shopper_seq", allocationSize = 1)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "nickname")
    private String nickname;

    @Column(name = "email")
    private String email;

    @Column(name = "name")
    private String name;

    @Builder.Default
    @Convert(converter = InteractionTypeListConverter.class)
    @Column(name = "interaction_type", nullable = false)
    private Set<InteractionType> interactionType = new HashSet<>();;

    @Enumerated(EnumType.STRING)
    @Column(name = "payment_format", nullable = false)
    private PaymentFormat paymentFormat;

    @Column(name = "priority", nullable = false)
    private boolean priority;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "personal_shopper_category",
            joinColumns = @JoinColumn(name = "personal_shopper_id"),
            inverseJoinColumns = @JoinColumn(name = "shopper_category_id")
    )
    @Builder.Default
    private Set<ShopperCategory> categories = new HashSet<>();

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "personal_shopper_brand",
            joinColumns = @JoinColumn(name = "personal_shopper_id"),
            inverseJoinColumns = @JoinColumn(name = "brand_id")
    )
    @Builder.Default
    private Set<Brand> brands = new HashSet<>();

    @Enumerated(EnumType.STRING)
    @Column(name = "access_source")
    private AccessSource accessSource;

    @Column(name = "date_shopper_status")
    private ZonedDateTime dateShopperStatus;

    @Column(name = "bitrix_id")
    private String bitrixId;
}