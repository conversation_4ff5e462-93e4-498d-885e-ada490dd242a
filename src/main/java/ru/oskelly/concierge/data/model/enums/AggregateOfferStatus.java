package ru.oskelly.concierge.data.model.enums;

import lombok.Getter;

import java.util.ResourceBundle;

/**
 * Агрегированные статусы оффера из OfferStatus
 * принцип агрегации https://confluence.oskelly.ru/pages/viewpage.action?pageId=15499270#id-[Яшопер]Биржазаявок-GUI-элементыстраницыСписокзаявок
 */
@Getter
public enum AggregateOfferStatus {
    ACTIVE("Активные"),
    COMPLETED("Завершенные"),
    ARCHIVED("Архивные");

    private final String description;

    AggregateOfferStatus(String description) {
        this.description = description;
    }

    /**
     * Получает локализованное описание статуса для указанной роли
     *
     * @param role роль пользователя
     * @return локализованное описание статуса
     */
    public String getDescription(Roles role) {
        if (role == null) {
            return description;
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_aggregate_offer_status";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return description;
        }
    }

    /**
     * Получает дополнительную информацию о статусе для указанной роли
     *
     * @param role роль пользователя
     * @return дополнительная информация о статусе
     */
    public String getStatusInfo(Roles role) {
        if (role == null) {
            return "-";
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_aggregate_offer_status_info";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return "-";
        }
    }

}
