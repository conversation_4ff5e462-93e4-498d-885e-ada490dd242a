package ru.oskelly.concierge.data.model.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

import java.util.ResourceBundle;

@Schema(
        description = "Статусы офера",
        example = "NEW"
)
@Getter
public enum OfferStatus {
    @Schema(description = "Новая заявка")
    NEW("Новая заявка", "Для начала работы с заявкой ознакомьтесь с запросом пользователя и нажмите кнопку «Взять в работу»"),

    @Schema(description = "Заявка взята в работу")
    IN_PROGRESS("В работе", "Заполните предложение и отправьте его менеджеру"),

    @Schema(description = "Заявка обработана")
    PROCESSED("Обработана", "Ваши предложения отправлены менеджеру"),

    @Schema(description = "Ожидаем решения покупателя")
    AWAITING_CUSTOMER_DECISION("Ожидаем решения покупателя", "Ваши предложения отправлены покупателю"),

    @Schema(description = "Заказ оплачен")
    ORDER_PAID("Заказ оплачен", "Заказ сформирован и оплачен покупателем"),

    @Schema(description = "Отмена по инициативе исполнителя")
    CANCELLED_BY_EXECUTOR("Отмена. Вы отклонили заявку", "Заявка отклонена по вашей инициативе"),

    @Schema(description = "Отмена по причине неактуальности")
    CANCELLED_NOT_RELEVANT("Отмена. Заявка не актуальна", "Заявка отклонена в связи с неактуальностью");

    private final String description;
    private final String localizedDescription;

    OfferStatus(String description, String localizedDescription) {
        this.description = description;
        this.localizedDescription = localizedDescription;
    }

    /**
     * Получает локализованное описание статуса для указанной роли
     *
     * @param role роль пользователя
     * @return локализованное описание статуса
     */
    public String getDescription(Roles role) {
        if (role == null) {
            return description;
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_offer_status";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return description;
        }
    }

    /**
     * Получает дополнительную информацию о статусе для указанной роли
     *
     * @param role роль пользователя
     * @return дополнительная информация о статусе
     */
    public String getStatusInfo(Roles role) {
        if (role == null) {
            return localizedDescription != null ? localizedDescription : "-";
        }
        try {
            String bundleName = "localization." + role.name().toLowerCase() + "_offer_status_info";
            ResourceBundle bundle = ResourceBundle.getBundle(bundleName);
            return bundle.getString(this.name());
        } catch (Exception e) {
            return localizedDescription != null ? localizedDescription : "-";
        }
    }

    public static boolean isValid(String value) {
        try {
            valueOf(value);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
