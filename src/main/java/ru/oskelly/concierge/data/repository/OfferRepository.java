package ru.oskelly.concierge.data.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ru.oskelly.concierge.data.model.Offer;
import ru.oskelly.concierge.data.model.ProposedOffer;
import ru.oskelly.concierge.data.model.QOffer;
import ru.oskelly.concierge.data.model.enums.OfferStatus;

import java.util.List;
import java.util.Optional;

@Repository
public interface OfferRepository extends BaseRepository<Offer, QOffer, Long> {

    long countByShipment_Id(Long id);

    long countByShipment_IdAndProductIdNotNull(Long shipmentId);

    long countByShipment_IdAndProductIdNull(Long id);

    List<Offer> findByShipment_IdAndProductIdNull(Long id);

    List<Offer> findByShipment_IdAndProductIdNotNull(Long id);

    @Query("SELECT DISTINCT po.id FROM Offer o JOIN o.shipment s JOIN s.purchaseOrder po WHERE o IN :offers")
    List<Long> findOrderIdsByOffers(@Param("offers") List<Offer> offers);

    @Query("""
        SELECT DISTINCT po.id FROM ProposedOffer pro
            JOIN pro.offer o
            JOIN o.shipment s
            JOIN s.purchaseOrder po
            WHERE pro IN :offers
    """)
    List<Long> findOrderIdsByProposedOffers(@Param("offers") List<ProposedOffer> offers);

    @Query("SELECT o FROM Offer o LEFT JOIN FETCH o.proposedOffers WHERE o.id = :offerId")
    Optional<Offer> findByIdWithProposedOffers(@Param("offerId") Long offerId);

    /**
     * Поиск офферов шоппера с фильтрацией по статусам и пагинацией
     * Загружает связанные данные для маппинга в ShortOfferDTO
     */
    @Query("""
        SELECT DISTINCT o FROM Offer o
        LEFT JOIN FETCH o.shipment s
        LEFT JOIN FETCH s.purchaseOrder po
        WHERE o.sellerId = :sellerId
        AND (:statuses IS NULL OR o.status IN :statuses)
        ORDER BY o.creationDate DESC
    """)
    Page<Offer> findShopperOffersWithDetails(
            @Param("sellerId") Long sellerId,
            @Param("statuses") List<OfferStatus> statuses,
            Pageable pageable
    );

    /**
     * Подсчет офферов шоппера с фильтрацией по статусам
     */
    @Query("""
        SELECT COUNT(o) FROM Offer o
        WHERE o.sellerId = :sellerId
        AND (:statuses IS NULL OR o.status IN :statuses)
    """)
    long countShopperOffers(
            @Param("sellerId") Long sellerId,
            @Param("statuses") List<OfferStatus> statuses
    );
}
